#!/usr/bin/env python3
"""
文档预处理脚本
处理docs目录下的所有文档，生成向量数据库和全文索引
"""
import sys
import os
from pathlib import Path
import time
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
from typing import List, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.config import get_config
from src.utils.logger import setup_logger, get_logger
from src.utils.helpers import (
    get_system_info,
    check_system_requirements,
    scan_documents,
    get_memory_usage,
    ProgressTracker
)
from src.utils.dependency_checker import get_dependency_checker
from src.core.document_processor import DocumentProcessor
from src.core.vector_store import VectorStore
from src.core.text_index import TextIndex
from src.core.pdf_preview_generator import PDFPreviewGenerator


def check_dependencies():
    """检查依赖包"""
    logger = get_logger(__name__)

    try:
        # 使用统一的依赖检查器
        checker = get_dependency_checker()
        return checker.validate_environment()
    except Exception as e:
        logger.error(f"依赖检查失败: {e}")
        return False


def process_single_document(doc_path: Path) -> Tuple[List, str]:
    """处理单个文档（用于并行处理）"""
    try:
        # 重新创建处理器实例（避免进程间共享问题）
        from src.core.document_processor import DocumentProcessor
        processor = DocumentProcessor()

        chunks = processor.process_document(doc_path)

        if chunks:
            return chunks, ""
        else:
            return [], f"文档 {doc_path.name} 未生成有效分块"

    except Exception as e:
        return [], f"处理文档失败 {doc_path}: {e}"


def process_documents_parallel(documents: List[Path], doc_processor) -> Tuple[List, List[str]]:
    """并行处理文档"""
    logger = get_logger(__name__)

    all_chunks = []
    failed_files = []

    # 计算合适的进程数
    max_workers = min(4, os.cpu_count() or 1)
    logger.info(f"使用 {max_workers} 个进程并行处理文档")

    progress = ProgressTracker(len(documents), "并行文档处理")

    try:
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_doc = {
                executor.submit(process_single_document, doc_path): doc_path
                for doc_path in documents
            }

            # 收集结果
            for future in as_completed(future_to_doc):
                doc_path = future_to_doc[future]
                try:
                    chunks, error_msg = future.result()

                    if chunks:
                        all_chunks.extend(chunks)
                        logger.info(f"文档 {doc_path.name} 生成 {len(chunks)} 个分块")
                    else:
                        logger.warning(error_msg or f"文档 {doc_path.name} 处理失败")
                        failed_files.append(str(doc_path))

                    progress.update()

                except Exception as e:
                    logger.error(f"获取处理结果失败 {doc_path}: {e}")
                    failed_files.append(str(doc_path))
                    progress.update()

    except Exception as e:
        logger.error(f"并行处理失败: {e}")
        # 降级到串行处理
        logger.info("降级到串行处理...")
        return process_documents_sequential(documents, doc_processor)

    progress.finish()
    return all_chunks, failed_files


def process_documents_sequential(documents: List[Path], doc_processor) -> Tuple[List, List[str]]:
    """串行处理文档"""
    logger = get_logger(__name__)

    all_chunks = []
    failed_files = []

    progress = ProgressTracker(len(documents), "串行文档处理")

    for doc_path in documents:
        try:
            logger.info(f"处理文档: {doc_path.name}")

            # 检查内存使用
            try:
                from src.utils.helpers import get_memory_usage
                memory_usage = get_memory_usage()
                if memory_usage['percent'] > 80:
                    logger.warning(f"内存使用率过高: {memory_usage['percent']:.1f}%")
            except Exception as e:
                logger.warning(f"无法获取内存使用情况: {e}")
                # 继续处理，不因为内存检查失败而中断

            # 处理文档
            chunks = doc_processor.process_document(doc_path)

            if chunks:
                all_chunks.extend(chunks)
                logger.info(f"文档 {doc_path.name} 生成 {len(chunks)} 个分块")
            else:
                logger.warning(f"文档 {doc_path.name} 未生成有效分块")
                failed_files.append(str(doc_path))

            progress.update()

        except Exception as e:
            logger.error(f"处理文档失败 {doc_path}: {e}")
            failed_files.append(str(doc_path))
            progress.update()
            continue

    progress.finish()
    return all_chunks, failed_files


def preprocess_documents(force_rebuild: bool = False):
    """预处理文档"""
    logger = get_logger(__name__)

    try:
        # 加载配置
        config = get_config()

        # 检查文档目录
        docs_dir = Path(config.docs_dir)
        if not docs_dir.exists():
            logger.error(f"文档目录不存在: {docs_dir}")
            return False

        # 扫描文档
        documents = scan_documents(docs_dir, config.processing.supported_formats)
        if not documents:
            logger.error("未找到支持的文档文件")
            return False

        logger.info(f"找到 {len(documents)} 个文档文件")

        # 按文件类型分类
        pdf_files = [doc for doc in documents if doc.suffix.lower() == '.pdf']
        word_files = [doc for doc in documents if doc.suffix.lower() in ['.docx', '.doc']]
        excel_files = [doc for doc in documents if doc.suffix.lower() in ['.xlsx', '.xls']]

        logger.info(f"文档类型统计: PDF {len(pdf_files)} 个, Word {len(word_files)} 个, Excel {len(excel_files)} 个")
        
        # 初始化处理器
        logger.info("初始化文档处理器...")
        doc_processor = DocumentProcessor()

        logger.info("初始化向量存储...")
        vector_store = VectorStore()

        logger.info("初始化全文索引...")
        text_index = TextIndex()

        logger.info("初始化PDF预览生成器...")
        pdf_preview_generator = PDFPreviewGenerator()

        # 清空现有数据
        if force_rebuild:
            logger.info("强制重建模式，清空现有数据...")
            vector_store.clear_collection()
            text_index.clear_index()
            pdf_preview_generator.clear_preview_files()
        else:
            clear_existing = input("是否清空现有的向量库、索引和预览文件？(y/N): ").lower().strip()
            if clear_existing == 'y':
                logger.info("清空现有数据...")
                vector_store.clear_collection()
                text_index.clear_index()
                pdf_preview_generator.clear_preview_files()
        
        # 处理文档
        logger.info("开始处理文档...")
        start_time = time.time()

        # 选择处理方式：并行或串行
        use_parallel = len(documents) > 5 and os.cpu_count() and os.cpu_count() > 1

        if use_parallel:
            all_chunks, failed_files = process_documents_parallel(documents, doc_processor)
        else:
            all_chunks, failed_files = process_documents_sequential(documents, doc_processor)
        
        if not all_chunks:
            logger.error("没有生成任何有效分块")
            return False
        
        logger.info(f"文档处理完成，总共生成 {len(all_chunks)} 个分块")
        
        # 构建向量数据库
        logger.info("构建向量数据库...")
        if not vector_store.batch_add_chunks(all_chunks):
            logger.error("向量数据库构建失败")
            return False
        
        # 构建全文索引
        logger.info("构建全文索引...")
        if not text_index.batch_add_chunks(all_chunks):
            logger.error("全文索引构建失败")
            return False

        # 生成PDF预览文件
        logger.info("生成PDF预览文件...")
        pdf_files = [doc for doc in documents if doc.suffix.lower() == '.pdf']
        if pdf_files:
            preview_results = pdf_preview_generator.batch_generate_previews(pdf_files)
            logger.info(f"PDF预览生成完成: 成功 {len(preview_results['success'])} 个, 失败 {len(preview_results['failed'])} 个")
        else:
            logger.info("未找到PDF文件，跳过预览生成")
        
        # 统计信息
        end_time = time.time()
        processing_time = end_time - start_time

        vector_stats = vector_store.get_collection_stats()
        index_stats = text_index.get_index_stats()

        # PDF预览统计
        pdf_preview_count = len(preview_results['success']) if pdf_files else 0

        logger.info("=== 预处理完成 ===")
        logger.info(f"处理时间: {processing_time:.2f} 秒")
        logger.info(f"成功处理: {len(documents) - len(failed_files)} 个文档")
        logger.info(f"失败文档: {len(failed_files)} 个")
        logger.info(f"生成分块: {len(all_chunks)} 个")
        logger.info(f"向量库分块数: {vector_stats.get('total_chunks', 0)}")
        logger.info(f"索引文档数: {index_stats.get('total_documents', 0)}")
        logger.info(f"PDF预览文件: {pdf_preview_count} 个")
        
        if failed_files:
            logger.warning("失败的文档:")
            for file in failed_files:
                logger.warning(f"  - {file}")
        
        return True
        
    except Exception as e:
        logger.error(f"预处理失败: {e}", exc_info=True)
        return False


def test_search():
    """测试搜索功能"""
    logger = get_logger(__name__)
    
    try:
        logger.info("测试搜索功能...")
        
        # 初始化
        vector_store = VectorStore()
        text_index = TextIndex()
        
        # 测试查询
        test_queries = [
            "安全管理制度",
            "财务管理",
            "人员培训",
            "船舶管理"
        ]
        
        for query in test_queries:
            logger.info(f"\n测试查询: '{query}'")
            
            # 向量搜索
            vector_results = vector_store.search(query, top_k=3)
            logger.info(f"向量搜索结果: {len(vector_results)} 个")
            for i, result in enumerate(vector_results):
                logger.info(f"  {i+1}. 相似度: {result['score']:.3f}, 来源: {result['metadata'].get('file_name', 'Unknown')}")
            
            # 全文搜索
            text_results = text_index.search(query, top_k=3)
            logger.info(f"全文搜索结果: {len(text_results)} 个")
            for i, result in enumerate(text_results):
                logger.info(f"  {i+1}. 分数: {result['score']:.3f}, 来源: {result.get('file_name', 'Unknown')}")
        
        logger.info("搜索测试完成")
        return True
        
    except Exception as e:
        logger.error(f"搜索测试失败: {e}")
        return False


def main():
    """主函数"""
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description="文档预处理脚本")
    parser.add_argument('--force', '-f', action='store_true',
                       help='强制重建所有索引和预览文件')
    parser.add_argument('--no-test', action='store_true',
                       help='跳过搜索功能测试')
    parser.add_argument('--log-level', default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')

    args = parser.parse_args()

    # 设置日志
    setup_logger(log_level=args.log_level)
    logger = get_logger(__name__)

    logger.info("=== 文档预处理开始 ===")

    if args.force:
        logger.info("🔄 强制重建模式已启用")

    # 检查环境
    if not check_system_requirements():
        logger.error("系统要求检查失败")
        return 1

    # 检查依赖
    if not check_dependencies():
        logger.error("依赖检查失败")
        return 1

    # 显示系统信息
    system_info = get_system_info()
    logger.info("=== 系统信息 ===")
    for key, value in system_info.items():
        logger.info(f"{key}: {value}")

    # 预处理文档
    if not preprocess_documents(force_rebuild=args.force):
        logger.error("文档预处理失败")
        return 1

    # 测试搜索
    if not args.no_test:
        test_choice = input("\n是否测试搜索功能？(Y/n): ").lower().strip()
        if test_choice != 'n':
            test_search()

    logger.info("=== 预处理完成 ===")
    return 0


if __name__ == "__main__":
    sys.exit(main())
