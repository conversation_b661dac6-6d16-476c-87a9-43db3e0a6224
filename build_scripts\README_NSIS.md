# LocalQA NSIS安装包构建指南

## 概述

NSIS (Nullsoft Scriptable Install System) 方案将您的AI应用打包成标准的Windows安装程序，包含完整的Python环境和所有依赖，用户无需预装任何软件即可使用。

## 优势

✅ **完整环境打包**: 包含Python解释器和所有依赖库  
✅ **AI模型兼容**: 避免PyInstaller对PyTorch/transformers的兼容性问题  
✅ **用户体验佳**: 标准Windows安装程序，支持卸载  
✅ **文件管理清晰**: 合理组织大型AI模型文件  
✅ **无需预装**: 用户机器无需Python环境  

## 前置要求

### 开发环境
- Windows 10/11
- Python 3.11+
- 网络连接（下载依赖）

### 需要安装的工具
1. **NSIS**: 从 https://nsis.sourceforge.io/Download 下载安装
2. **Visual Studio Build Tools** (可选): 用于编译某些Python包

## 构建步骤

### 方法一: 一键构建（推荐）

```bash
# 运行一键构建脚本
build_scripts\build_all.bat
```

### 方法二: 分步构建

```bash
# 1. 构建便携式Python环境
python build_scripts\prepare_portable_env.py

# 2. 构建NSIS安装程序
python build_scripts\build_installer.py
```

## 构建过程详解

### 第一阶段: 便携式环境准备
1. 下载Python 3.11 嵌入式版本
2. 配置Python环境（启用site-packages）
3. 安装pip包管理器
4. 安装项目所有依赖
5. 复制应用程序文件
6. 创建启动脚本

### 第二阶段: NSIS安装程序构建
1. 准备构建环境
2. 下载Visual C++ Redistributable
3. 创建许可证文件
4. 编译NSIS脚本
5. 生成最终安装程序

## 输出文件

构建成功后会生成：
- `LocalQA_Setup_1.0.0.exe` - 最终安装程序（约500MB-1GB）

## 安装程序功能

### 用户安装体验
1. 双击运行安装程序
2. 选择安装路径（默认：Program Files\LocalQA）
3. 选择组件（主程序 + VC++运行库）
4. 自动安装完成
5. 创建桌面和开始菜单快捷方式

### 安装后目录结构
```
C:\Program Files\LocalQA\
├── python\              # 完整Python环境
│   ├── python.exe
│   ├── Lib\
│   └── Scripts\
├── app\                  # 应用程序
│   ├── main.py
│   ├── src\
│   ├── models\
│   └── static\
├── LocalQA.bat          # 启动脚本
└── start_localqa.py     # Python启动脚本
```

## 自定义配置

### 修改安装程序信息
编辑 `build_scripts/LocalQA_installer.nsi`:

```nsis
!define PRODUCT_NAME "您的应用名称"
!define PRODUCT_VERSION "1.0.0"
!define PRODUCT_PUBLISHER "您的公司名称"
!define PRODUCT_WEB_SITE "http://www.yourcompany.com"
```

### 添加应用图标
1. 准备 `.ico` 格式图标文件
2. 放置在 `static/icon.ico`
3. 修改NSIS脚本中的图标路径

### 修改安装路径
编辑NSIS脚本中的：
```nsis
InstallDir "$PROGRAMFILES64\您的应用名称"
```

## 故障排除

### 常见问题

**Q: NSIS编译失败**
- 检查NSIS是否正确安装
- 确认所有文件路径正确
- 查看编译输出的错误信息

**Q: 安装程序过大**
- 检查是否包含了不必要的文件
- 考虑移除开发工具依赖
- 使用NSIS的高压缩选项

**Q: 用户安装后无法启动**
- 确认Visual C++ Redistributable已安装
- 检查Python环境配置
- 查看应用程序日志

**Q: AI功能不工作**
- 确认模型文件已正确复制
- 检查依赖库是否完整
- 验证CUDA/PyTorch配置

### 调试技巧

1. **测试便携式环境**:
   ```bash
   cd build_portable
   LocalQA.bat
   ```

2. **检查Python环境**:
   ```bash
   build_portable\python\python.exe -c "import torch; print(torch.__version__)"
   ```

3. **验证依赖**:
   ```bash
   build_portable\python\Scripts\pip.exe list
   ```

## 分发建议

### 内部分发
- 通过内网文件服务器分发
- 提供安装说明文档
- 建立技术支持渠道

### 版本管理
- 使用语义化版本号
- 维护更新日志
- 提供增量更新机制

### 用户培训
- 准备用户手册
- 录制操作视频
- 组织培训会议

## 性能优化

### 减小安装包大小
1. 移除不必要的Python库
2. 压缩模型文件
3. 使用NSIS高级压缩

### 提升安装速度
1. 优化文件组织结构
2. 减少文件数量
3. 使用固态硬盘测试

### 运行时优化
1. 预编译Python字节码
2. 优化启动脚本
3. 配置环境变量

## 总结

NSIS方案为您的AI应用提供了专业的Windows分发解决方案，特别适合：
- 企业内部部署
- 无技术背景的最终用户
- 需要标准安装/卸载体验的场景

通过本指南，您可以创建一个完整、可靠的安装程序，确保用户能够顺利使用您的AI应用。
