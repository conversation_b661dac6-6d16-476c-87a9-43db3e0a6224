"""
日志管理模块
"""
import sys
import os
from pathlib import Path
from loguru import logger
from typing import Optional

from .config import get_config


class LoggerManager:
    """日志管理器"""
    
    def __init__(self):
        self._initialized = False
        self.config = None
    
    def setup_logger(self, log_level: Optional[str] = None, 
                    log_file: Optional[str] = None):
        """设置日志器"""
        if self._initialized:
            return

        try:
            self.config = get_config()
            level = log_level or getattr(self.config, 'log_level', 'INFO')
        except Exception as e:
            # 如果配置加载失败，使用默认值
            self.config = None
            level = log_level or 'INFO'

        # 移除默认处理器
        logger.remove()
        
        # 控制台输出
        try:
            # 在打包环境中，sys.stdout可能为None，需要检查
            if sys.stdout is not None:
                logger.add(
                    sys.stdout,
                    level=level,
                    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                           "<level>{level: <8}</level> | "
                           "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                           "<level>{message}</level>",
                    colorize=True
                )
        except Exception as e:
            # 如果控制台输出失败，忽略错误
            pass
        
        # 文件输出
        try:
            log_path = log_file or getattr(self.config, 'log_file', None)

            # 如果配置中没有日志文件路径，使用默认路径
            if not log_path:
                # 获取程序运行目录
                try:
                    if getattr(sys, 'frozen', False):
                        # 打包后的程序
                        app_dir = Path(sys.executable).parent
                    else:
                        # 开发环境
                        app_dir = Path(__file__).parent.parent.parent

                    log_path = app_dir / "data" / "logs" / "app.log"
                except Exception:
                    # 如果路径获取失败，使用临时目录
                    import tempfile
                    log_path = Path(tempfile.gettempdir()) / "localqa_app.log"

            if log_path:
                # 确保日志目录存在
                log_file_path = Path(log_path)
                log_file_path.parent.mkdir(parents=True, exist_ok=True)

                logger.add(
                    str(log_path),
                    level=level,
                    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | "
                           "{name}:{function}:{line} | {message}",
                    rotation="10 MB",
                    retention="30 days",
                    compression="zip",
                    encoding="utf-8"
                )
        except Exception as e:
            # 如果文件日志完全失败，忽略错误
            pass

        self._initialized = True
        try:
            logger.info("日志系统初始化完成")
        except Exception:
            # 如果日志输出失败，忽略错误
            pass
    
    def get_logger(self, name: Optional[str] = None):
        """获取日志器"""
        if not self._initialized:
            self.setup_logger()
        
        if name:
            return logger.bind(name=name)
        return logger


# 全局日志管理器
log_manager = LoggerManager()


def get_logger(name: Optional[str] = None):
    """获取日志器"""
    return log_manager.get_logger(name)


def setup_logger(log_level: Optional[str] = None, 
                log_file: Optional[str] = None):
    """设置日志器"""
    log_manager.setup_logger(log_level, log_file)
