"""
自定义异常类定义
"""
from typing import Optional, Any, Dict
from .constants import ErrorCode


class LocalQAException(Exception):
    """LocalQA应用基础异常类"""
    
    def __init__(self, message: str, error_code: ErrorCode = ErrorCode.SYSTEM_ERROR, 
                 details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def __str__(self) -> str:
        return f"[{self.error_code.name}] {self.message}"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'error_code': self.error_code.value,
            'error_name': self.error_code.name,
            'message': self.message,
            'details': self.details
        }


class ConfigurationError(LocalQAException):
    """配置错误异常"""
    
    def __init__(self, message: str, config_key: Optional[str] = None, 
                 config_value: Optional[Any] = None):
        details = {}
        if config_key:
            details['config_key'] = config_key
        if config_value is not None:
            details['config_value'] = str(config_value)
        
        super().__init__(message, ErrorCode.CONFIG_ERROR, details)


class ModelLoadError(LocalQAException):
    """模型加载错误异常"""
    
    def __init__(self, message: str, model_name: Optional[str] = None, 
                 model_path: Optional[str] = None):
        details = {}
        if model_name:
            details['model_name'] = model_name
        if model_path:
            details['model_path'] = model_path
        
        super().__init__(message, ErrorCode.MODEL_LOAD_ERROR, details)


class DocumentProcessError(LocalQAException):
    """文档处理错误异常"""
    
    def __init__(self, message: str, file_path: Optional[str] = None, 
                 file_type: Optional[str] = None, page_number: Optional[int] = None):
        details = {}
        if file_path:
            details['file_path'] = file_path
        if file_type:
            details['file_type'] = file_type
        if page_number is not None:
            details['page_number'] = page_number
        
        super().__init__(message, ErrorCode.DOCUMENT_PROCESS_ERROR, details)


class SearchError(LocalQAException):
    """搜索错误异常"""
    
    def __init__(self, message: str, query: Optional[str] = None, 
                 search_type: Optional[str] = None):
        details = {}
        if query:
            details['query'] = query
        if search_type:
            details['search_type'] = search_type
        
        super().__init__(message, ErrorCode.SEARCH_ERROR, details)


class ValidationError(LocalQAException):
    """数据验证错误异常"""
    
    def __init__(self, message: str, field_name: Optional[str] = None, 
                 field_value: Optional[Any] = None, expected_type: Optional[str] = None):
        details = {}
        if field_name:
            details['field_name'] = field_name
        if field_value is not None:
            details['field_value'] = str(field_value)
        if expected_type:
            details['expected_type'] = expected_type
        
        super().__init__(message, ErrorCode.CONFIG_ERROR, details)


class ResourceError(LocalQAException):
    """资源错误异常（内存、磁盘等）"""
    
    def __init__(self, message: str, resource_type: Optional[str] = None, 
                 current_usage: Optional[float] = None, limit: Optional[float] = None):
        details = {}
        if resource_type:
            details['resource_type'] = resource_type
        if current_usage is not None:
            details['current_usage'] = current_usage
        if limit is not None:
            details['limit'] = limit
        
        super().__init__(message, ErrorCode.SYSTEM_ERROR, details)


class DatabaseError(LocalQAException):
    """数据库操作错误异常"""
    
    def __init__(self, message: str, operation: Optional[str] = None, 
                 table_name: Optional[str] = None):
        details = {}
        if operation:
            details['operation'] = operation
        if table_name:
            details['table_name'] = table_name
        
        super().__init__(message, ErrorCode.SYSTEM_ERROR, details)


class NetworkError(LocalQAException):
    """网络错误异常"""
    
    def __init__(self, message: str, url: Optional[str] = None, 
                 status_code: Optional[int] = None, timeout: Optional[float] = None):
        details = {}
        if url:
            details['url'] = url
        if status_code is not None:
            details['status_code'] = status_code
        if timeout is not None:
            details['timeout'] = timeout
        
        super().__init__(message, ErrorCode.SYSTEM_ERROR, details)


# 异常处理装饰器
def handle_exceptions(default_return=None, log_error=True):
    """异常处理装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except LocalQAException as e:
                if log_error:
                    from .logger import get_logger
                    logger = get_logger(func.__module__)
                    logger.error(f"LocalQA异常: {e}")
                if default_return is not None:
                    return default_return
                raise
            except Exception as e:
                if log_error:
                    from .logger import get_logger
                    logger = get_logger(func.__module__)
                    logger.error(f"未处理异常: {e}", exc_info=True)
                if default_return is not None:
                    return default_return
                raise LocalQAException(f"未处理的异常: {str(e)}")
        return wrapper
    return decorator


# 异常上下文管理器
class ExceptionContext:
    """异常上下文管理器"""
    
    def __init__(self, operation_name: str, logger=None):
        self.operation_name = operation_name
        self.logger = logger
        if self.logger is None:
            from .logger import get_logger
            self.logger = get_logger(__name__)
    
    def __enter__(self):
        self.logger.debug(f"开始执行: {self.operation_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is None:
            self.logger.debug(f"成功完成: {self.operation_name}")
        else:
            if issubclass(exc_type, LocalQAException):
                self.logger.error(f"LocalQA异常 in {self.operation_name}: {exc_val}")
            else:
                self.logger.error(f"未处理异常 in {self.operation_name}: {exc_val}", exc_info=True)
        return False  # 不抑制异常
