"""
CNKI风格制度检索界面
"""
import math
from typing import List, Optional

try:
    from PyQt6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLineEdit,
        QPushButton, QLabel, QTableWidget, QTableWidgetItem,
        QHeaderView, QFrame, QMessageBox
    )
    from PyQt6.QtCore import Qt, QThread, pyqtSignal
    from PyQt6.QtGui import QFont, QIcon
except ImportError as e:
    raise e

from ..utils.logger import get_logger
from ..core.search_system import get_search_system

logger = get_logger(__name__)


class SearchThread(QThread):
    """搜索线程"""
    results_ready = pyqtSignal(list, int)  # 结果列表, 总数
    error_occurred = pyqtSignal(str)
    
    def __init__(self, query: str):
        super().__init__()
        self.query = query
    
    def run(self):
        """执行搜索"""
        try:
            search_system = get_search_system()

            # 执行搜索，获取更多结果
            search_response = search_system.search_documents(
                query=self.query,
                limit=50,  # 获取更多结果
                offset=0
            )

            # 提取结果列表和总数
            results = search_response.get('results', [])
            total_count = search_response.get('total', 0)

            # 转换SearchResult对象为字典
            results_dict = []
            for result in results:
                if hasattr(result, 'to_dict'):
                    results_dict.append(result.to_dict())
                else:
                    results_dict.append(result)

            self.results_ready.emit(results_dict, total_count)

        except Exception as e:
            logger.error(f"搜索失败: {e}")
            self.error_occurred.emit(str(e))


class CNKISearchInterface(QWidget):
    """CNKI风格制度检索界面"""
    preview_requested = pyqtSignal(str, int)  # 文件路径, 页码
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.search_system = None
        self.search_thread = None
        self.total_results = 0
        self.current_query = ""
        
        self._init_ui()
        self._init_search_system()
    
    def _init_ui(self):
        """初始化界面"""
        try:
            layout = QVBoxLayout(self)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(20)
            
            # 创建搜索区域
            self._create_search_area(layout)
            
            # 创建结果表格
            self._create_results_table(layout)
            
        except Exception as e:
            logger.error(f"制度检索界面初始化失败: {e}")
    
    def _create_search_area(self, parent_layout):
        """创建搜索区域"""
        try:
            search_frame = QFrame()
            search_frame.setObjectName("card")
            search_frame.setStyleSheet("""
                QFrame#card {
                    background-color: white;
                    border: 1px solid #e0e6ed;
                    border-radius: 8px;
                    padding: 15px;
                }
            """)
            
            search_layout = QVBoxLayout(search_frame)
            
            # 标题
            title_label = QLabel("🔍 制度检索")
            title_label.setStyleSheet("""
                QLabel {
                    font-size: 16pt;
                    font-weight: bold;
                    color: #1a5099;
                    margin-bottom: 10px;
                }
            """)
            search_layout.addWidget(title_label)
            
            # 搜索栏
            search_row = QHBoxLayout()
            
            self.search_bar = QLineEdit()
            self.search_bar.setPlaceholderText("输入关键词，例如：报销标准")
            self.search_bar.setStyleSheet("""
                QLineEdit {
                    border: 1px solid #d9d9d9;
                    border-radius: 6px;
                    padding: 10px 15px;
                    font-size: 11pt;
                    background-color: white;
                }
                QLineEdit:focus {
                    border-color: #1a5099;
                }
            """)
            self.search_bar.returnPressed.connect(self._on_search_clicked)
            
            self.search_btn = QPushButton("🔍 搜索")
            self.search_btn.setFixedSize(100, 40)
            self.search_btn.setStyleSheet("""
                QPushButton {
                    background-color: #1a5099;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    font-size: 11pt;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #0d3d73;
                }
                QPushButton:disabled {
                    background-color: #ccc;
                }
            """)
            self.search_btn.clicked.connect(self._on_search_clicked)
            
            search_row.addWidget(self.search_bar)
            search_row.addWidget(self.search_btn)
            search_layout.addLayout(search_row)
            
            parent_layout.addWidget(search_frame)
            
        except Exception as e:
            logger.error(f"搜索区域创建失败: {e}")
    
    def _create_results_table(self, parent_layout):
        """创建搜索结果表格"""
        try:
            results_frame = QFrame()
            results_frame.setObjectName("card")
            results_frame.setStyleSheet("""
                QFrame#card {
                    background-color: white;
                    border: 1px solid #e0e6ed;
                    border-radius: 8px;
                    padding: 15px;
                }
            """)
            
            results_layout = QVBoxLayout(results_frame)
            
            # 结果标题
            self.results_title = QLabel("📋 搜索结果")
            self.results_title.setStyleSheet("""
                QLabel {
                    font-size: 14pt;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 10px;
                }
            """)
            results_layout.addWidget(self.results_title)
            
            # 搜索结果表格（CNKI风格）
            self.results_table = QTableWidget()
            self.results_table.setColumnCount(4)
            self.results_table.setHorizontalHeaderLabels(["相关度", "文档标题", "部门", "内容摘要"])
            
            # 设置列宽
            self.results_table.setColumnWidth(0, 80)   # 相关度列
            self.results_table.setColumnWidth(1, 250)  # 标题列
            self.results_table.setColumnWidth(2, 120)  # 部门列
            self.results_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)  # 摘要列自适应
            
            # 设置表格样式
            self.results_table.setStyleSheet("""
                QTableWidget {
                    gridline-color: #e0e6ed;
                    alternate-background-color: #f8fafc;
                    background-color: white;
                    border: 1px solid #e0e6ed;
                    border-radius: 6px;
                    selection-background-color: #e6f0ff;
                }
                
                QHeaderView::section {
                    background-color: #1a5099;
                    color: white;
                    padding: 8px;
                    border: none;
                    font-weight: bold;
                    font-size: 10pt;
                }
                
                QTableWidget::item {
                    padding: 8px;
                    border-bottom: 1px solid #f0f0f0;
                }
                
                QTableWidget::item:selected {
                    background-color: #e6f0ff;
                    color: #1a5099;
                }
            """)
            
            # 设置表格属性
            self.results_table.setAlternatingRowColors(True)
            self.results_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
            self.results_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
            self.results_table.verticalHeader().setVisible(False)
            
            # 连接双击事件
            self.results_table.itemDoubleClicked.connect(self._on_row_double_clicked)
            
            results_layout.addWidget(self.results_table)
            parent_layout.addWidget(results_frame)
            
        except Exception as e:
            logger.error(f"结果表格创建失败: {e}")
    

    
    def _init_search_system(self):
        """初始化搜索系统"""
        try:
            self.search_system = get_search_system()
        except Exception as e:
            logger.error(f"搜索系统初始化失败: {e}")
    
    def _on_search_clicked(self):
        """搜索按钮点击事件"""
        try:
            query = self.search_bar.text().strip()
            if not query:
                QMessageBox.warning(self, "提示", "请输入搜索关键词")
                return
            
            self.current_query = query
            self._perform_search()
            
        except Exception as e:
            logger.error(f"搜索处理失败: {e}")
    
    def _perform_search(self):
        """执行搜索"""
        try:
            # 禁用搜索按钮
            self.search_btn.setEnabled(False)
            self.search_btn.setText("搜索中...")
            
            # 清空表格
            self.results_table.setRowCount(0)
            self.results_title.setText("📋 搜索中...")
            
            # 启动搜索线程
            self.search_thread = SearchThread(self.current_query)
            self.search_thread.results_ready.connect(self._on_results_ready)
            self.search_thread.error_occurred.connect(self._on_search_error)
            self.search_thread.start()
            
        except Exception as e:
            logger.error(f"搜索执行失败: {e}")
            self._reset_search_button()
    
    def _on_results_ready(self, results: List[dict], total_count: int):
        """处理搜索结果"""
        try:
            self.total_results = total_count
            self._display_results(results)
            self._reset_search_button()

        except Exception as e:
            logger.error(f"结果处理失败: {e}")
            self._reset_search_button()
    
    def _on_search_error(self, error: str):
        """处理搜索错误"""
        try:
            QMessageBox.critical(self, "搜索失败", f"搜索时出现错误：{error}")
            self.results_title.setText("📋 搜索结果")
            self._reset_search_button()
            
        except Exception as e:
            logger.error(f"搜索错误处理失败: {e}")
            self._reset_search_button()
    
    def _display_results(self, results: List[dict]):
        """显示搜索结果"""
        try:
            self.results_table.setRowCount(len(results))
            
            for row, result in enumerate(results):
                # 相关度显示星级
                score = result.get('score', 0)
                stars_count = int(score * 5)
                stars = "★" * stars_count + "☆" * (5 - stars_count)
                score_text = f"{stars} ({score:.2f})"
                
                # 文档标题
                title = result.get('file_name', '未知文档')
                
                # 部门
                department = result.get('department', '未知部门')
                
                # 内容摘要（高亮关键词）
                content = result.get('content', '')
                summary = self._create_highlighted_summary(content, self.current_query)
                
                # 设置表格项
                self.results_table.setItem(row, 0, QTableWidgetItem(score_text))
                self.results_table.setItem(row, 1, QTableWidgetItem(title))
                self.results_table.setItem(row, 2, QTableWidgetItem(department))
                self.results_table.setItem(row, 3, QTableWidgetItem(summary))
                
                # 存储完整数据
                self.results_table.item(row, 0).setData(Qt.ItemDataRole.UserRole, result)
            
            # 更新标题
            self.results_title.setText(f"📋 搜索结果 (共 {self.total_results} 条)")
            
        except Exception as e:
            logger.error(f"结果显示失败: {e}")
    
    def _create_highlighted_summary(self, content: str, query: str) -> str:
        """创建高亮摘要"""
        try:
            # 简化处理：截取前100个字符作为摘要
            summary = content[:100] + "..." if len(content) > 100 else content
            
            # 这里可以添加关键词高亮逻辑
            # 由于QTableWidget不支持HTML，这里暂时返回纯文本
            return summary
            
        except Exception as e:
            logger.error(f"摘要创建失败: {e}")
            return content[:100] + "..." if len(content) > 100 else content
    

    
    def _on_row_double_clicked(self, item):
        """表格行双击事件"""
        try:
            row = item.row()
            result_data = self.results_table.item(row, 0).data(Qt.ItemDataRole.UserRole)

            if result_data:
                file_path = result_data.get('source_file', '')

                if file_path:
                    # 直接打开PDF文档
                    self._open_pdf_document(file_path)

        except Exception as e:
            logger.error(f"行双击处理失败: {e}")

    def _open_pdf_document(self, file_path):
        """打开PDF文档"""
        try:
            import os
            import subprocess
            import platform

            if not os.path.exists(file_path):
                QMessageBox.warning(self, "文件不存在", f"文件不存在：{file_path}")
                return

            # 根据操作系统选择打开方式
            system = platform.system()
            if system == "Windows":
                os.startfile(file_path)
            elif system == "Darwin":  # macOS
                subprocess.run(["open", file_path])
            else:  # Linux
                subprocess.run(["xdg-open", file_path])

            logger.info(f"已打开PDF文档: {file_path}")

        except Exception as e:
            logger.error(f"打开PDF文档失败: {e}")
            QMessageBox.critical(self, "打开失败", f"无法打开PDF文档：{str(e)}")
    
    def _reset_search_button(self):
        """重置搜索按钮"""
        try:
            self.search_btn.setEnabled(True)
            self.search_btn.setText("🔍 搜索")
            
        except Exception as e:
            logger.error(f"重置搜索按钮失败: {e}")
