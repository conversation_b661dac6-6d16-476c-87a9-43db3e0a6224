"""
CNKI风格智能问答界面
"""
import time
from typing import List, Optional

try:
    from PyQt6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, 
        QPushButton, QLabel, QScrollArea, QFrame,
        QListWidget, QListWidgetItem, QTextBrowser
    )
    from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
    from PyQt6.QtGui import QFont, QTextCursor, QColor
except ImportError as e:
    raise e

from ..utils.logger import get_logger
from ..core.search_system import get_search_system

logger = get_logger(__name__)


class AIQueryThread(QThread):
    """AI查询线程"""
    result_ready = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, query: str):
        super().__init__()
        self.query = query
    
    def run(self):
        """执行AI查询"""
        try:
            search_system = get_search_system()
            response = search_system.ai_question(self.query)
            self.result_ready.emit(response.to_dict())
            
        except Exception as e:
            logger.error(f"AI查询失败: {e}")
            self.error_occurred.emit(str(e))


class CNKIQAInterface(QWidget):
    """CNKI风格智能问答界面"""
    preview_requested = pyqtSignal(str, int)  # 文件路径, 页码
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.search_system = None
        self.query_thread = None
        
        self._init_ui()
        self._init_search_system()
    
    def _init_ui(self):
        """初始化界面"""
        try:
            layout = QVBoxLayout(self)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(20)
            
            # 创建输入区域
            self._create_input_area(layout)
            
            # 创建答案显示区
            self._create_answer_area(layout)
            
            # 创建来源引用区
            self._create_sources_area(layout)
            
        except Exception as e:
            logger.error(f"智能问答界面初始化失败: {e}")
    
    def _create_input_area(self, parent_layout):
        """创建输入区域"""
        try:
            input_frame = QFrame()
            input_frame.setObjectName("card")
            input_frame.setStyleSheet("""
                QFrame#card {
                    background-color: white;
                    border: 1px solid #e0e6ed;
                    border-radius: 8px;
                    padding: 15px;
                }
            """)
            
            input_layout = QVBoxLayout(input_frame)
            
            # 标题
            title_label = QLabel("💬 智能问答")
            title_label.setStyleSheet("""
                QLabel {
                    font-size: 16pt;
                    font-weight: bold;
                    color: #1a5099;
                    margin-bottom: 10px;
                }
            """)
            input_layout.addWidget(title_label)
            
            # 输入框和按钮的水平布局
            input_row = QHBoxLayout()
            
            # 输入框
            self.input_box = QTextEdit()
            self.input_box.setPlaceholderText("请输入您的问题，例如：年假如何计算？")
            self.input_box.setFixedHeight(80)
            self.input_box.setStyleSheet("""
                QTextEdit {
                    border: 1px solid #d9d9d9;
                    border-radius: 6px;
                    padding: 10px;
                    font-size: 11pt;
                    background-color: white;
                }
                QTextEdit:focus {
                    border-color: #1a5099;
                }
            """)
            
            # 提问按钮
            self.ask_btn = QPushButton("提问")
            self.ask_btn.setFixedSize(100, 80)
            self.ask_btn.setStyleSheet("""
                QPushButton {
                    background-color: #1a5099;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    font-size: 12pt;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #0d3d73;
                }
                QPushButton:disabled {
                    background-color: #ccc;
                }
            """)
            self.ask_btn.clicked.connect(self._on_ask_clicked)
            
            input_row.addWidget(self.input_box)
            input_row.addWidget(self.ask_btn)
            input_layout.addLayout(input_row)
            
            parent_layout.addWidget(input_frame)
            
        except Exception as e:
            logger.error(f"输入区域创建失败: {e}")
    
    def _create_answer_area(self, parent_layout):
        """创建答案显示区"""
        try:
            answer_frame = QFrame()
            answer_frame.setObjectName("card")
            answer_frame.setStyleSheet("""
                QFrame#card {
                    background-color: white;
                    border: 1px solid #e0e6ed;
                    border-radius: 8px;
                    padding: 15px;
                }
            """)
            
            answer_layout = QVBoxLayout(answer_frame)
            
            # 答案标题
            answer_title = QLabel("📝 答案：")
            answer_title.setStyleSheet("""
                QLabel {
                    font-size: 14pt;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 10px;
                }
            """)
            answer_layout.addWidget(answer_title)
            
            # 答案显示区（CNKI风格引用格式）
            self.answer_area = QTextBrowser()
            self.answer_area.setOpenLinks(False)  # 自定义链接处理
            self.answer_area.setMinimumHeight(200)
            self.answer_area.setStyleSheet("""
                QTextBrowser {
                    border: 1px solid #f0f0f0;
                    border-radius: 6px;
                    padding: 15px;
                    background-color: #fafafa;
                    font-size: 11pt;
                    line-height: 1.6;
                }
            """)
            
            # 设置初始内容
            welcome_html = """
            <div style='color: #666; font-style: italic; text-align: center; margin-top: 50px;'>
                <h3 style='color: #1a5099;'>🤖 AI智能助手</h3>
                <p>欢迎使用公司制度智能问答系统！</p>
                <p>您可以询问关于公司制度、规定、流程等相关问题。</p>
                <div style='margin-top: 20px; text-align: left; max-width: 400px; margin-left: auto; margin-right: auto;'>
                    <p><strong>示例问题：</strong></p>
                    <ul>
                        <li>"公司的安全管理制度是什么？"</li>
                        <li>"员工培训有哪些要求？"</li>
                        <li>"财务报销流程是怎样的？"</li>
                    </ul>
                </div>
            </div>
            """
            self.answer_area.setHtml(welcome_html)
            self.answer_area.anchorClicked.connect(self._on_link_clicked)
            
            answer_layout.addWidget(self.answer_area)
            parent_layout.addWidget(answer_frame)
            
        except Exception as e:
            logger.error(f"答案区域创建失败: {e}")
    
    def _create_sources_area(self, parent_layout):
        """创建来源引用区"""
        try:
            sources_frame = QFrame()
            sources_frame.setObjectName("card")
            sources_frame.setStyleSheet("""
                QFrame#card {
                    background-color: white;
                    border: 1px solid #e0e6ed;
                    border-radius: 8px;
                    padding: 15px;
                }
            """)
            
            sources_layout = QVBoxLayout(sources_frame)
            
            # 来源标题
            sources_title = QLabel("📚 来源引用：")
            sources_title.setStyleSheet("""
                QLabel {
                    font-size: 14pt;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 10px;
                }
            """)
            sources_layout.addWidget(sources_title)
            
            # 来源列表
            self.sources_list = QListWidget()
            self.sources_list.setFixedHeight(150)
            self.sources_list.setStyleSheet("""
                QListWidget {
                    border: 1px solid #f0f0f0;
                    border-radius: 6px;
                    background-color: #fafafa;
                }
                QListWidget::item {
                    padding: 8px;
                    border-bottom: 1px solid #f0f0f0;
                }
                QListWidget::item:hover {
                    background-color: #e6f0ff;
                }
                QListWidget::item:selected {
                    background-color: #1a5099;
                    color: white;
                }
            """)
            self.sources_list.itemClicked.connect(self._on_source_clicked)
            
            sources_layout.addWidget(self.sources_list)
            parent_layout.addWidget(sources_frame)
            
        except Exception as e:
            logger.error(f"来源区域创建失败: {e}")
    
    def _init_search_system(self):
        """初始化搜索系统"""
        try:
            self.search_system = get_search_system()
        except Exception as e:
            logger.error(f"搜索系统初始化失败: {e}")
    
    def _on_ask_clicked(self):
        """提问按钮点击事件"""
        try:
            query = self.input_box.toPlainText().strip()
            if not query:
                return
            
            # 禁用按钮
            self.ask_btn.setEnabled(False)
            self.ask_btn.setText("思考中...")
            
            # 清空之前的结果
            self.answer_area.setHtml("<div style='text-align: center; margin-top: 50px; color: #666;'>🤔 正在思考您的问题...</div>")
            self.sources_list.clear()
            
            # 启动AI查询线程
            self.query_thread = AIQueryThread(query)
            self.query_thread.result_ready.connect(self._on_result_ready)
            self.query_thread.error_occurred.connect(self._on_error_occurred)
            self.query_thread.start()
            
        except Exception as e:
            logger.error(f"提问处理失败: {e}")
            self._reset_ask_button()
    
    def _on_result_ready(self, result: dict):
        """处理查询结果"""
        try:
            answer = result.get('answer', '抱歉，无法生成回答。')
            sources = result.get('sources', [])
            
            # 显示答案（CNKI风格格式）
            self._display_answer(answer, sources)
            
            # 显示来源
            self._display_sources(sources)
            
            # 重置按钮
            self._reset_ask_button()
            
        except Exception as e:
            logger.error(f"结果处理失败: {e}")
            self._reset_ask_button()
    
    def _on_error_occurred(self, error: str):
        """处理错误"""
        try:
            error_html = f"""
            <div style='text-align: center; margin-top: 50px; color: #ff4d4f;'>
                <h3>❌ 处理失败</h3>
                <p>抱歉，处理您的问题时出现错误：</p>
                <p style='font-style: italic;'>{error}</p>
            </div>
            """
            self.answer_area.setHtml(error_html)
            self._reset_ask_button()
            
        except Exception as e:
            logger.error(f"错误处理失败: {e}")
            self._reset_ask_button()
    
    def _display_answer(self, answer: str, sources: List[dict]):
        """显示答案（CNKI风格格式）"""
        try:
            # 构建CNKI风格的答案HTML
            answer_html = f"""
            <div style='font-size: 12pt; line-height: 1.8; color: #333;'>
                <div style='background-color: #f0f8ff; padding: 15px; border-left: 4px solid #1a5099; margin-bottom: 15px;'>
                    {answer}
                </div>
            """
            
            # 添加来源引用
            if sources:
                answer_html += "<div style='margin-top: 20px; font-size: 10pt; color: #666;'>"
                answer_html += "<strong>参考来源：</strong><br>"
                for i, source in enumerate(sources[:3], 1):  # 最多显示3个来源
                    file_name = source.get('file_name', '未知文件')
                    page_num = source.get('page_number', 1)
                    answer_html += f"<p style='margin: 5px 0;'>"
                    answer_html += f"[{i}] <a href='source://{source.get('source_file', '')}#page={page_num}' style='color: #1a5099; text-decoration: none;'>"
                    answer_html += f"{file_name} (第{page_num}页)</a></p>"
                
                answer_html += "</div>"
            
            answer_html += "</div>"
            
            self.answer_area.setHtml(answer_html)
            
        except Exception as e:
            logger.error(f"答案显示失败: {e}")
    
    def _display_sources(self, sources: List[dict]):
        """显示来源列表"""
        try:
            self.sources_list.clear()
            
            if not sources:
                item = QListWidgetItem("暂无相关来源")
                item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsSelectable)
                self.sources_list.addItem(item)
                return
            
            for source in sources:
                file_name = source.get('file_name', '未知文件')
                department = source.get('department', '未知部门')
                score = source.get('score', 0)
                
                # 创建星级显示
                stars = "★" * int(score * 5) + "☆" * (5 - int(score * 5))
                
                item_text = f"{stars} {file_name} - {department}"
                item = QListWidgetItem(item_text)
                item.setData(Qt.ItemDataRole.UserRole, source)
                self.sources_list.addItem(item)
                
        except Exception as e:
            logger.error(f"来源显示失败: {e}")
    
    def _on_link_clicked(self, url):
        """处理链接点击"""
        try:
            url_str = url.toString()
            if url_str.startswith('source://'):
                # 解析文件路径和页码
                parts = url_str.replace('source://', '').split('#page=')
                file_path = parts[0]
                page_num = int(parts[1]) if len(parts) > 1 else 1
                
                # 发送预览请求
                self.preview_requested.emit(file_path, page_num)
                
        except Exception as e:
            logger.error(f"链接点击处理失败: {e}")
    
    def _on_source_clicked(self, item):
        """来源项点击事件"""
        try:
            source_data = item.data(Qt.ItemDataRole.UserRole)
            if source_data:
                file_path = source_data.get('source_file', '')
                page_num = source_data.get('page_number', 1)
                
                if file_path:
                    self.preview_requested.emit(file_path, page_num)
                    
        except Exception as e:
            logger.error(f"来源点击处理失败: {e}")
    
    def _reset_ask_button(self):
        """重置提问按钮"""
        try:
            self.ask_btn.setEnabled(True)
            self.ask_btn.setText("提问")
            
        except Exception as e:
            logger.error(f"重置按钮失败: {e}")
