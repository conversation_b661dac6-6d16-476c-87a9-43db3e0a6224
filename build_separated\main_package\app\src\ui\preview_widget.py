"""
文档预览组件
"""
import os
from pathlib import Path
from typing import Optional

try:
    from PyQt6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLabel,
        QPushButton, QScrollArea, QFrame, QTextEdit,
        QSpinBox, QMessageBox
    )
    from PyQt6.QtCore import Qt, QThread, pyqtSignal, QUrl
    from PyQt6.QtGui import QFont, QPixmap, QPainter
    try:
        from PyQt6.QtWebEngineWidgets import QWebEngineView
        print("✅ QWebEngineView导入成功")
    except ImportError as web_import_error:
        print(f"❌ QWebEngineView导入失败: {web_import_error}")
        QWebEngineView = None
except ImportError as e:
    raise e

try:
    import fitz  # PyMuPDF
except ImportError:
    fitz = None

from ..utils.logger import get_logger
from ..utils.config import get_config

logger = get_logger(__name__)


class DocumentLoader(QThread):
    """文档加载线程"""
    document_loaded = pyqtSignal(str, int)  # HTML内容, 总页数
    error_occurred = pyqtSignal(str)
    
    def __init__(self, file_path: str, page: int = 1):
        super().__init__()
        self.file_path = file_path
        self.page = page
    
    def run(self):
        """加载文档"""
        try:
            file_path = Path(self.file_path)
            
            if not file_path.exists():
                self.error_occurred.emit(f"文件不存在: {file_path}")
                return
            
            if file_path.suffix.lower() == '.pdf':
                html_content, total_pages = self._load_pdf(file_path, self.page)
            else:
                html_content, total_pages = self._load_other_format(file_path)
            
            self.document_loaded.emit(html_content, total_pages)
            
        except Exception as e:
            logger.error(f"文档加载失败: {e}")
            self.error_occurred.emit(str(e))
    
    def _load_pdf(self, file_path: Path, page: int) -> tuple:
        """加载PDF文档"""
        if fitz is None:
            return self._load_as_text(file_path)
        
        try:
            doc = fitz.open(file_path)
            total_pages = len(doc)
            
            if page < 1 or page > total_pages:
                page = 1
            
            # 获取指定页面
            pdf_page = doc[page - 1]
            
            # 转换为HTML
            html_content = self._pdf_page_to_html(pdf_page, page, total_pages)
            
            doc.close()
            return html_content, total_pages
            
        except Exception as e:
            logger.error(f"PDF加载失败: {e}")
            return self._load_as_text(file_path)
    
    def _pdf_page_to_html(self, page, page_num: int, total_pages: int) -> str:
        """将PDF页面转换为HTML"""
        try:
            # 提取文本
            text = page.get_text()
            
            # 如果文本为空，尝试OCR（这里简化处理）
            if not text.strip():
                text = f"第{page_num}页内容无法提取文本，可能是图片或扫描版PDF。"
            
            # 构建HTML
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <style>
                    body {{
                        font-family: 'Microsoft YaHei', Arial, sans-serif;
                        line-height: 1.6;
                        margin: 20px;
                        background-color: #f9f9f9;
                    }}
                    .page-header {{
                        background-color: #007acc;
                        color: white;
                        padding: 4px 10px;
                        border-radius: 3px;
                        margin-bottom: 15px;
                        text-align: center;
                        height: 24px;
                        line-height: 24px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }}
                    .content {{
                        background-color: white;
                        padding: 20px;
                        border-radius: 5px;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                        white-space: pre-wrap;
                        word-wrap: break-word;
                    }}
                    .page-header h3 {{
                        margin: 0;
                        font-size: 14px;
                        font-weight: normal;
                    }}
                </style>
            </head>
            <body>
                <div class="page-header">
                    <h3>第 {page_num} 页 / 共 {total_pages} 页</h3>
                </div>
                <div class="content">
                    {text}
                </div>
            </body>
            </html>
            """
            
            return html
            
        except Exception as e:
            logger.error(f"PDF页面转换失败: {e}")
            return f"<html><body><p>页面加载失败: {str(e)}</p></body></html>"
    
    def _load_other_format(self, file_path: Path) -> tuple:
        """加载其他格式文档"""
        return self._load_as_text(file_path)
    
    def _load_as_text(self, file_path: Path) -> tuple:
        """作为文本加载"""
        try:
            # 尝试读取文本内容
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            except UnicodeDecodeError:
                with open(file_path, 'r', encoding='gbk') as f:
                    content = f.read()
            
            # 构建HTML
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <style>
                    body {{
                        font-family: 'Microsoft YaHei', Arial, sans-serif;
                        line-height: 1.6;
                        margin: 20px;
                        background-color: #f9f9f9;
                    }}
                    .file-header {{
                        background-color: #007acc;
                        color: white;
                        padding: 4px 10px;
                        border-radius: 3px;
                        margin-bottom: 15px;
                        text-align: center;
                        height: 24px;
                        line-height: 24px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }}
                    .content {{
                        background-color: white;
                        padding: 20px;
                        border-radius: 5px;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                        white-space: pre-wrap;
                        word-wrap: break-word;
                    }}
                    .file-header h3 {{
                        margin: 0;
                        font-size: 14px;
                        font-weight: normal;
                    }}
                </style>
            </head>
            <body>
                <div class="file-header">
                    <h3>{file_path.name}</h3>
                </div>
                <div class="content">
                    {content}
                </div>
            </body>
            </html>
            """
            
            return html, 1
            
        except Exception as e:
            logger.error(f"文本加载失败: {e}")
            error_html = f"""
            <html>
            <body>
                <h3>文档加载失败</h3>
                <p>文件: {file_path.name}</p>
                <p>错误: {str(e)}</p>
            </body>
            </html>
            """
            return error_html, 1


class PreviewWidget(QWidget):
    """文档预览组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.config = get_config()
        self.current_file = None
        self.current_page = 1
        self.total_pages = 1
        self.loader_thread = None
        
        self._init_ui()
    
    def _init_ui(self):
        """初始化界面"""
        try:
            layout = QVBoxLayout(self)
            
            # 工具栏
            self._create_toolbar(layout)
            
            # 预览区域
            self._create_preview_area(layout)
            
            # 显示欢迎信息
            self._show_welcome()
            
        except Exception as e:
            logger.error(f"预览界面初始化失败: {e}")
    
    def _create_toolbar(self, parent_layout):
        """创建工具栏"""
        try:
            toolbar_frame = QFrame()
            toolbar_frame.setFixedHeight(28)  # 设置固定高度
            toolbar_frame.setStyleSheet("""
                QFrame {
                    border: 1px solid #ddd;
                    border-radius: 3px;
                    background-color: #f0f0f0;
                    padding: 2px 8px;
                    max-height: 28px;
                    min-height: 28px;
                }
            """)
            toolbar_layout = QHBoxLayout(toolbar_frame)
            
            # 文件名标签
            self.file_label = QLabel("未选择文档")
            self.file_label.setFont(QFont("", 9, QFont.Weight.Bold))
            self.file_label.setStyleSheet("""
                color: #333;
                padding: 0;
                margin: 0;
                font-size: 9pt;
            """)
            toolbar_layout.addWidget(self.file_label)
            
            toolbar_layout.addStretch()

            # 页面控制区域已删除，只保留文件名显示
            
            parent_layout.addWidget(toolbar_frame)
            
        except Exception as e:
            logger.error(f"工具栏创建失败: {e}")
    
    def _create_preview_area(self, parent_layout):
        """创建预览区域"""
        try:
            # 尝试使用QWebEngineView
            if QWebEngineView is not None:
                self.preview_view = QWebEngineView()
                self.preview_view.setStyleSheet("""
                    QWebEngineView {
                        border: 1px solid #ddd;
                        border-radius: 5px;
                        background-color: white;
                    }
                """)
            else:
                # 备用：使用QTextEdit
                self.preview_view = QTextEdit()
                self.preview_view.setReadOnly(True)
                self.preview_view.setStyleSheet("""
                    QTextEdit {
                        border: 1px solid #ddd;
                        border-radius: 5px;
                        background-color: white;
                        font-family: 'Microsoft YaHei', Arial, sans-serif;
                    }
                """)
            
            parent_layout.addWidget(self.preview_view)
            
        except Exception as e:
            logger.error(f"预览区域创建失败: {e}")
    
    def _show_welcome(self):
        """显示欢迎信息"""
        try:
            welcome_html = """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <style>
                    body {
                        font-family: 'Microsoft YaHei', Arial, sans-serif;
                        text-align: center;
                        padding: 50px;
                        background-color: #f9f9f9;
                        color: #666;
                    }
                    .welcome-icon {
                        font-size: 48px;
                        margin-bottom: 20px;
                    }
                    .welcome-title {
                        font-size: 24px;
                        margin-bottom: 10px;
                        color: #333;
                    }
                    .welcome-text {
                        font-size: 16px;
                        line-height: 1.6;
                    }
                </style>
            </head>
            <body>
                <div class="welcome-icon">📄</div>
                <div class="welcome-title">文档预览</div>
                <div class="welcome-text">
                    点击参考来源链接<br>
                    即可在此处预览相关文档内容
                </div>
            </body>
            </html>
            """
            
            if hasattr(self.preview_view, 'setHtml'):
                self.preview_view.setHtml(welcome_html)
            else:
                self.preview_view.setPlainText("文档预览区域\n\n点击搜索结果或AI问答的来源链接即可预览文档")
            
        except Exception as e:
            logger.error(f"欢迎信息显示失败: {e}")

    def show_welcome(self):
        """显示欢迎信息（公共方法）"""
        # 重置文件标签显示
        if hasattr(self, 'file_label'):
            self.file_label.setText("未选择文档")

        # 重置当前文件信息
        self.current_file = None
        self.current_page = 1

        # 显示欢迎信息
        self._show_welcome()

    def load_document(self, file_path: str, page: int = 1):
        """加载文档"""
        try:
            logger.info(f"加载文档: {file_path}, 页码: {page}")

            self.current_file = file_path
            self.current_page = page

            # 更新文件名显示
            file_name = Path(file_path).name
            self.file_label.setText(f"📄 {file_name}")

            # 检查文件是否存在
            if not Path(file_path).exists():
                self._show_error(f"文件不存在: {file_path}")
                return

            # 检查是否为PDF文件
            if file_path.lower().endswith('.pdf'):
                logger.info(f"检测到PDF文件，尝试直接加载: {file_path}")
                # 检查QWebEngineView是否可用
                if (hasattr(self.preview_view, 'load') and
                    hasattr(self.preview_view, 'setUrl') and
                    QWebEngineView is not None):
                    logger.info("使用QWebEngineView直接加载PDF")
                    self._load_pdf_directly(file_path, page)
                else:
                    logger.info("QWebEngineView不可用，使用文本方式加载PDF")
                    self._fallback_to_text_loading(file_path, page)
            else:
                logger.info(f"非PDF文件，使用标准加载方式: {file_path}")
                # 使用原有的线程加载方式
                self.loader_thread = DocumentLoader(file_path, page)
                self.loader_thread.document_loaded.connect(self._on_document_loaded)
                self.loader_thread.error_occurred.connect(self._on_load_error)
                self.loader_thread.start()

                # 显示加载中
                self._show_loading()

        except Exception as e:
            logger.error(f"文档加载启动失败: {e}")
            self._show_error(f"加载失败: {str(e)}")

    def _load_pdf_directly(self, file_path: str, page: int = 1):
        """使用PDF.js直接加载PDF文件"""
        try:
            from PyQt6.QtCore import QUrl
            import urllib.parse

            # 转换为绝对路径
            abs_path = Path(file_path).absolute()

            # 检查文件是否存在
            if not abs_path.exists():
                logger.error(f"PDF文件不存在: {abs_path}")
                self._show_error(f"文件不存在: {file_path}")
                return

            # 检查QWebEngineView是否可用
            if not hasattr(self.preview_view, 'load'):
                logger.warning("QWebEngineView不可用，回退到文本方式")
                self._fallback_to_text_loading(file_path, page)
                return

            # 获取PDF.js查看器的路径
            current_dir = Path(__file__).parent.parent.parent  # 回到项目根目录
            pdfjs_viewer_path = current_dir / "static" / "pdfjs" / "pdf_viewer.html"

            if not pdfjs_viewer_path.exists():
                logger.error(f"PDF.js查看器不存在: {pdfjs_viewer_path}")
                self._fallback_to_text_loading(file_path, page)
                return

            # 创建PDF.js查看器URL
            viewer_url = QUrl.fromLocalFile(str(pdfjs_viewer_path.absolute()))

            logger.info(f"使用PDF.js加载PDF: {abs_path}")
            logger.info(f"PDF.js查看器路径: {viewer_url.toString()}")

            # 显示加载中状态
            self._show_loading()

            # 加载PDF.js查看器
            self.preview_view.load(viewer_url)

            # 存储PDF信息，等待查看器加载完成后发送
            self._pending_pdf_info = {
                'pdf_path': abs_path,
                'page': page
            }

            # 连接加载完成信号
            if hasattr(self.preview_view, 'loadFinished'):
                try:
                    self.preview_view.loadFinished.disconnect()
                except:
                    pass
                self.preview_view.loadFinished.connect(self._on_pdfjs_load_finished)

            # 页面控制已删除，无需更新

        except Exception as e:
            logger.error(f"PDF.js加载失败: {e}")
            self._fallback_to_text_loading(file_path, page)

    def _on_pdfjs_load_finished(self, success):
        """PDF.js查看器加载完成回调"""
        try:
            if success:
                logger.info("PDF.js查看器加载成功")
                # 发送PDF文件信息给查看器
                if hasattr(self, '_pending_pdf_info'):
                    self._send_pdf_to_viewer()
            else:
                logger.warning("PDF.js查看器加载失败，回退到文本方式")
                # 回退到文本加载方式
                if self.current_file:
                    self._fallback_to_text_loading(self.current_file, self.current_page)
        except Exception as e:
            logger.error(f"PDF.js加载完成处理失败: {e}")

    def _send_pdf_to_viewer(self):
        """向PDF.js查看器发送PDF文件信息"""
        try:
            if not hasattr(self, '_pending_pdf_info'):
                return

            from PyQt6.QtCore import QUrl

            pdf_info = self._pending_pdf_info
            pdf_path = pdf_info['pdf_path']
            page = pdf_info['page']

            # 创建PDF文件URL
            pdf_url = QUrl.fromLocalFile(str(pdf_path)).toString()

            # 构建JavaScript代码来加载PDF
            js_code = f"""
            if (window.pdfViewer) {{
                window.pdfViewer.loadPDF('{pdf_url}', {page});
            }} else {{
                // 如果查看器还没准备好，等待一下再试
                setTimeout(function() {{
                    if (window.pdfViewer) {{
                        window.pdfViewer.loadPDF('{pdf_url}', {page});
                    }}
                }}, 500);
            }}
            """

            # 执行JavaScript
            if hasattr(self.preview_view, 'page') and hasattr(self.preview_view.page(), 'runJavaScript'):
                self.preview_view.page().runJavaScript(js_code)
                logger.info(f"已发送PDF加载指令: {pdf_path}, 页码: {page}")

            # 清除待处理信息
            delattr(self, '_pending_pdf_info')

        except Exception as e:
            logger.error(f"发送PDF信息失败: {e}")
            # 回退到文本方式
            if self.current_file:
                self._fallback_to_text_loading(self.current_file, self.current_page)

    def _fallback_to_text_loading(self, file_path: str, page: int = 1):
        """回退到文本加载方式"""
        try:
            logger.info("使用文本方式加载PDF")
            self.loader_thread = DocumentLoader(file_path, page)
            self.loader_thread.document_loaded.connect(self._on_document_loaded)
            self.loader_thread.error_occurred.connect(self._on_load_error)
            self.loader_thread.start()
            self._show_loading()
        except Exception as e:
            logger.error(f"文本方式加载也失败: {e}")
            self._show_error(f"加载失败: {str(e)}")

    def _show_loading(self):
        """显示加载中"""
        try:
            loading_html = """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <style>
                    body {
                        font-family: 'Microsoft YaHei', Arial, sans-serif;
                        text-align: center;
                        padding: 50px;
                        background-color: #f9f9f9;
                        color: #666;
                    }
                    .loading-icon {
                        font-size: 48px;
                        margin-bottom: 20px;
                        animation: spin 2s linear infinite;
                    }
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                </style>
            </head>
            <body>
                <div class="loading-icon">⏳</div>
                <div>正在加载文档...</div>
            </body>
            </html>
            """
            
            if hasattr(self.preview_view, 'setHtml'):
                self.preview_view.setHtml(loading_html)
            else:
                self.preview_view.setPlainText("正在加载文档...")
            
        except Exception as e:
            logger.error(f"加载中显示失败: {e}")
    
    def _on_document_loaded(self, html_content: str, total_pages: int):
        """文档加载完成"""
        try:
            self.total_pages = total_pages
            
            # 页面控件已删除，无需更新
            
            # 显示内容
            if hasattr(self.preview_view, 'setHtml'):
                self.preview_view.setHtml(html_content)
            else:
                # 从HTML中提取文本内容
                import re
                text_content = re.sub(r'<[^>]+>', '', html_content)
                self.preview_view.setPlainText(text_content)
            
            logger.info(f"文档加载完成: {self.current_file}, 第{self.current_page}页")
            
        except Exception as e:
            logger.error(f"文档显示失败: {e}")
            self._show_error(f"显示失败: {str(e)}")
    
    def _on_load_error(self, error: str):
        """加载错误"""
        try:
            self._show_error(error)
            
        except Exception as e:
            logger.error(f"错误处理失败: {e}")
    
    def _show_error(self, error: str):
        """显示错误"""
        try:
            error_html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <style>
                    body {{
                        font-family: 'Microsoft YaHei', Arial, sans-serif;
                        text-align: center;
                        padding: 50px;
                        background-color: #f9f9f9;
                        color: #d32f2f;
                    }}
                    .error-icon {{
                        font-size: 48px;
                        margin-bottom: 20px;
                    }}
                </style>
            </head>
            <body>
                <div class="error-icon">❌</div>
                <div>加载失败</div>
                <div style="margin-top: 10px; font-size: 14px;">{error}</div>
            </body>
            </html>
            """
            
            if hasattr(self.preview_view, 'setHtml'):
                self.preview_view.setHtml(error_html)
            else:
                self.preview_view.setPlainText(f"加载失败: {error}")
            
        except Exception as e:
            logger.error(f"错误显示失败: {e}")
    
    # 页面导航方法已删除，因为页面控制功能已移除
    
    # _update_page_buttons 方法已删除，因为页面控制按钮已移除
