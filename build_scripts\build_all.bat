@echo off
chcp 65001 >nul
echo LocalQA NSIS安装包构建脚本
echo ================================

echo.
echo 第1步: 检查Python环境...
python --version
if errorlevel 1 (
    echo 错误: 未找到Python环境
    pause
    exit /b 1
)

echo.
echo 第2步: 安装构建依赖...
pip install requests

echo.
echo 第3步: 构建便携式Python环境...
python build_scripts\prepare_portable_env.py
if errorlevel 1 (
    echo 错误: 便携式环境构建失败
    pause
    exit /b 1
)

echo.
echo 第4步: 构建NSIS安装程序...
python build_scripts\build_installer.py
if errorlevel 1 (
    echo 错误: NSIS安装程序构建失败
    pause
    exit /b 1
)

echo.
echo 构建完成!
echo 安装程序已生成: LocalQA_Setup_1.0.0.exe
echo.
pause
