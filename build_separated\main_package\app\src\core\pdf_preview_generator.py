"""
PDF预览文件生成器
为PDF文档生成预览文件，支持缩略图和HTML预览
"""
import os
import shutil
import io
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import json
import base64

try:
    import fitz  # PyMuPDF
except ImportError:
    fitz = None

try:
    from PIL import Image
except ImportError:
    Image = None

from ..utils.config import get_config
from ..utils.logger import get_logger
from ..utils.helpers import ensure_dir, safe_filename, ProgressTracker

logger = get_logger(__name__)


class PDFPreviewGenerator:
    """PDF预览文件生成器"""
    
    def __init__(self):
        self.config = get_config()
        self.preview_dir = Path(self.config.preview_dir)
        
        # 确保预览目录存在
        ensure_dir(self.preview_dir)
        
        # 预览配置
        self.thumbnail_size = (200, 280)  # 缩略图尺寸
        self.preview_dpi = 150  # 预览图DPI
        self.max_preview_pages = 10  # 最大预览页数
        
    def generate_pdf_preview(self, pdf_path: Path) -> Dict[str, Any]:
        """为PDF文档生成预览文件"""
        if fitz is None:
            logger.error("PyMuPDF未安装，无法生成PDF预览")
            return {}
        
        try:
            logger.info(f"开始生成PDF预览: {pdf_path.name}")
            
            # 创建预览文件夹
            safe_name = safe_filename(pdf_path.stem)
            preview_folder = self.preview_dir / safe_name
            ensure_dir(preview_folder)
            
            # 打开PDF文档
            doc = fitz.open(pdf_path)
            total_pages = len(doc)
            
            preview_info = {
                'file_name': pdf_path.name,
                'file_path': str(pdf_path),
                'total_pages': total_pages,
                'preview_folder': str(preview_folder),
                'thumbnails': [],
                'preview_pages': [],
                'metadata': {}
            }
            
            # 提取文档元数据
            metadata = doc.metadata
            if metadata:
                preview_info['metadata'] = {
                    'title': metadata.get('title', ''),
                    'author': metadata.get('author', ''),
                    'subject': metadata.get('subject', ''),
                    'creator': metadata.get('creator', ''),
                    'producer': metadata.get('producer', ''),
                    'creation_date': metadata.get('creationDate', ''),
                    'modification_date': metadata.get('modDate', '')
                }
            
            # 生成缩略图和预览页
            preview_pages = min(total_pages, self.max_preview_pages)
            
            for page_num in range(preview_pages):
                page = doc[page_num]
                
                # 生成缩略图
                thumbnail_path = self._generate_thumbnail(page, page_num + 1, preview_folder)
                if thumbnail_path:
                    preview_info['thumbnails'].append({
                        'page': page_num + 1,
                        'path': str(thumbnail_path),
                        'relative_path': str(thumbnail_path.relative_to(self.preview_dir))
                    })
                
                # 生成预览页图像
                preview_path = self._generate_preview_image(page, page_num + 1, preview_folder)
                if preview_path:
                    preview_info['preview_pages'].append({
                        'page': page_num + 1,
                        'path': str(preview_path),
                        'relative_path': str(preview_path.relative_to(self.preview_dir))
                    })
            
            # 生成HTML预览文件
            html_path = self._generate_html_preview(preview_info, preview_folder)
            if html_path:
                preview_info['html_preview'] = str(html_path)
            
            # 保存预览信息
            info_path = preview_folder / 'preview_info.json'
            with open(info_path, 'w', encoding='utf-8') as f:
                json.dump(preview_info, f, ensure_ascii=False, indent=2)
            
            doc.close()
            
            logger.info(f"PDF预览生成完成: {pdf_path.name}, 生成了 {len(preview_info['thumbnails'])} 个缩略图")
            return preview_info
            
        except Exception as e:
            logger.error(f"生成PDF预览失败 {pdf_path}: {e}")
            return {}
    
    def _generate_thumbnail(self, page, page_num: int, preview_folder: Path) -> Optional[Path]:
        """生成缩略图"""
        try:
            # 生成缩略图
            pix = page.get_pixmap(matrix=fitz.Matrix(0.5, 0.5))  # 缩放到50%
            img_data = pix.tobytes("png")
            
            if Image:
                # 使用PIL调整尺寸
                img = Image.open(io.BytesIO(img_data))
                img.thumbnail(self.thumbnail_size, Image.Resampling.LANCZOS)
                
                thumbnail_path = preview_folder / f"thumbnail_page_{page_num:03d}.png"
                img.save(thumbnail_path, "PNG")
            else:
                # 直接保存原始图像
                thumbnail_path = preview_folder / f"thumbnail_page_{page_num:03d}.png"
                with open(thumbnail_path, 'wb') as f:
                    f.write(img_data)
            
            return thumbnail_path
            
        except Exception as e:
            logger.error(f"生成缩略图失败 页面{page_num}: {e}")
            return None
    
    def _generate_preview_image(self, page, page_num: int, preview_folder: Path) -> Optional[Path]:
        """生成预览图像"""
        try:
            # 生成高质量预览图
            mat = fitz.Matrix(self.preview_dpi / 72, self.preview_dpi / 72)
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("png")
            
            preview_path = preview_folder / f"preview_page_{page_num:03d}.png"
            with open(preview_path, 'wb') as f:
                f.write(img_data)
            
            return preview_path
            
        except Exception as e:
            logger.error(f"生成预览图像失败 页面{page_num}: {e}")
            return None
    
    def _generate_html_preview(self, preview_info: Dict[str, Any], preview_folder: Path) -> Optional[Path]:
        """生成HTML预览文件"""
        try:
            html_content = self._create_html_template(preview_info)
            
            html_path = preview_folder / "preview.html"
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            return html_path
            
        except Exception as e:
            logger.error(f"生成HTML预览失败: {e}")
            return None
    
    def _create_html_template(self, preview_info: Dict[str, Any]) -> str:
        """创建HTML预览模板"""
        file_name = preview_info.get('file_name', '未知文档')
        total_pages = preview_info.get('total_pages', 0)
        metadata = preview_info.get('metadata', {})
        preview_pages = preview_info.get('preview_pages', [])
        
        # 构建页面图像HTML
        pages_html = ""
        for page_info in preview_pages:
            page_num = page_info['page']
            img_path = Path(page_info['path']).name
            pages_html += f"""
            <div class="page-preview">
                <h3>第 {page_num} 页</h3>
                <img src="{img_path}" alt="第{page_num}页预览" class="preview-image">
            </div>
            """
        
        # 构建元数据HTML
        metadata_html = ""
        if metadata:
            metadata_html = f"""
            <div class="metadata">
                <h3>文档信息</h3>
                <table>
                    <tr><td>标题:</td><td>{metadata.get('title', '无')}</td></tr>
                    <tr><td>作者:</td><td>{metadata.get('author', '无')}</td></tr>
                    <tr><td>主题:</td><td>{metadata.get('subject', '无')}</td></tr>
                    <tr><td>创建者:</td><td>{metadata.get('creator', '无')}</td></tr>
                    <tr><td>总页数:</td><td>{total_pages}</td></tr>
                </table>
            </div>
            """
        
        html_template = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{file_name} - 预览</title>
            <style>
                body {{
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                }}
                .container {{
                    max-width: 1200px;
                    margin: 0 auto;
                    background: white;
                    padding: 20px;
                    border-radius: 8px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }}
                .header {{
                    text-align: center;
                    margin-bottom: 30px;
                    padding-bottom: 20px;
                    border-bottom: 2px solid #1a5099;
                }}
                .header h1 {{
                    color: #1a5099;
                    margin: 0;
                }}
                .metadata {{
                    margin-bottom: 30px;
                    padding: 15px;
                    background: #e6f0ff;
                    border-radius: 5px;
                }}
                .metadata table {{
                    width: 100%;
                    border-collapse: collapse;
                }}
                .metadata td {{
                    padding: 5px 10px;
                    border-bottom: 1px solid #ddd;
                }}
                .metadata td:first-child {{
                    font-weight: bold;
                    width: 100px;
                }}
                .page-preview {{
                    margin-bottom: 30px;
                    text-align: center;
                }}
                .page-preview h3 {{
                    color: #1a5099;
                    margin-bottom: 10px;
                }}
                .preview-image {{
                    max-width: 100%;
                    height: auto;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                }}
                .footer {{
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #ddd;
                    color: #666;
                    font-size: 12px;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>{file_name}</h1>
                    <p>PDF文档预览</p>
                </div>
                
                {metadata_html}
                
                <div class="preview-content">
                    {pages_html}
                </div>
                
                <div class="footer">
                    <p>由公司制度查询平台生成 - 预览页面</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html_template
    
    def batch_generate_previews(self, pdf_files: List[Path]) -> Dict[str, Any]:
        """批量生成PDF预览"""
        logger.info(f"开始批量生成PDF预览，共 {len(pdf_files)} 个文件")
        
        results = {
            'success': [],
            'failed': [],
            'total': len(pdf_files)
        }
        
        progress = ProgressTracker(len(pdf_files), "PDF预览生成")
        
        for pdf_file in pdf_files:
            try:
                if pdf_file.suffix.lower() == '.pdf':
                    preview_info = self.generate_pdf_preview(pdf_file)
                    if preview_info:
                        results['success'].append({
                            'file': str(pdf_file),
                            'preview_info': preview_info
                        })
                    else:
                        results['failed'].append(str(pdf_file))
                else:
                    logger.warning(f"跳过非PDF文件: {pdf_file}")
                
                progress.update()
                
            except Exception as e:
                logger.error(f"处理PDF预览失败 {pdf_file}: {e}")
                results['failed'].append(str(pdf_file))
                progress.update()
        
        progress.finish()
        
        logger.info(f"PDF预览生成完成: 成功 {len(results['success'])} 个, 失败 {len(results['failed'])} 个")
        return results
    
    def clear_preview_files(self) -> bool:
        """清空预览文件"""
        try:
            if self.preview_dir.exists():
                shutil.rmtree(self.preview_dir)
                ensure_dir(self.preview_dir)
                logger.info("PDF预览文件已清空")
                return True
            return True
            
        except Exception as e:
            logger.error(f"清空PDF预览文件失败: {e}")
            return False
    
    def get_preview_info(self, pdf_path: Path) -> Optional[Dict[str, Any]]:
        """获取PDF预览信息"""
        try:
            safe_name = safe_filename(pdf_path.stem)
            preview_folder = self.preview_dir / safe_name
            info_path = preview_folder / 'preview_info.json'
            
            if info_path.exists():
                with open(info_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            
            return None
            
        except Exception as e:
            logger.error(f"获取预览信息失败 {pdf_path}: {e}")
            return None
