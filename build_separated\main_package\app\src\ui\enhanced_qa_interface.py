"""
增强版问答界面 - 支持会话管理和历史记录
"""
import sys
from pathlib import Path
from typing import Optional, List
from datetime import datetime

try:
    from PyQt6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
        QTextEdit, QLineEdit, QPushButton, QListWidget, QListWidgetItem,
        QLabel, QFrame, QScrollArea, QMessageBox, QMenu, QInputDialog,
        QFileDialog, QToolButton, QButtonGroup, QSizePolicy
    )
    from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
    from PyQt6.QtGui import QFont, QIcon, QAction, QTextCursor
except ImportError:
    # 占位符
    QWidget = QVBoxLayout = QHBoxLayout = QSplitter = object
    QTextEdit = QLineEdit = QPushButton = QListWidget = QListWidgetItem = object
    QLabel = QFrame = QScrollArea = QMessageBox = QMenu = QInputDialog = object
    QFileDialog = QToolButton = QButtonGroup = object
    Qt = QThread = pyqtSignal = QTimer = object
    QFont = QIcon = QAction = QTextCursor = object

from ..utils.logger import get_logger
from ..core.search_system import get_search_system
from ..core.session_manager import get_session_manager, ChatSession, ChatMessage
from .preview_widget import PreviewWidget

logger = get_logger(__name__)


class AIQueryThread(QThread):
    """AI查询线程"""
    result_ready = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, query: str):
        super().__init__()
        self.query = query
    
    def run(self):
        """执行AI查询"""
        try:
            search_system = get_search_system()
            response = search_system.ai_question(self.query)
            self.result_ready.emit(response.to_dict())
        except Exception as e:
            logger.error(f"AI查询失败: {e}")
            self.error_occurred.emit(str(e))


class SessionListWidget(QListWidget):
    """会话列表组件"""
    session_selected = pyqtSignal(str)  # session_id
    session_deleted = pyqtSignal(str)   # session_id
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.session_manager = get_session_manager()
        self.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.customContextMenuRequested.connect(self._show_context_menu)
        self.itemClicked.connect(self._on_item_clicked)
        self._refresh_sessions()
    
    def _refresh_sessions(self):
        """刷新会话列表"""
        try:
            self.clear()
            sessions = self.session_manager.list_sessions()
            
            for session in sessions:
                item = QListWidgetItem()
                item.setText(session.title)
                item.setData(Qt.ItemDataRole.UserRole, session.id)
                
                # 设置样式
                if session.id == self.session_manager.current_session.id:
                    item.setBackground(Qt.GlobalColor.lightGray)
                
                self.addItem(item)
                
        except Exception as e:
            logger.error(f"刷新会话列表失败: {e}")
    
    def _on_item_clicked(self, item):
        """会话项点击事件"""
        session_id = item.data(Qt.ItemDataRole.UserRole)
        if session_id:
            self.session_selected.emit(session_id)
    
    def _show_context_menu(self, position):
        """显示右键菜单"""
        item = self.itemAt(position)
        if not item:
            return
        
        session_id = item.data(Qt.ItemDataRole.UserRole)
        menu = QMenu(self)
        
        # 重命名
        rename_action = QAction("重命名", self)
        rename_action.triggered.connect(lambda: self._rename_session(session_id))
        menu.addAction(rename_action)
        
        # 导出
        export_action = QAction("导出", self)
        export_action.triggered.connect(lambda: self._export_session(session_id))
        menu.addAction(export_action)
        
        menu.addSeparator()
        
        # 删除
        delete_action = QAction("删除", self)
        delete_action.triggered.connect(lambda: self._delete_session(session_id))
        menu.addAction(delete_action)
        
        menu.exec(self.mapToGlobal(position))
    
    def _rename_session(self, session_id: str):
        """重命名会话"""
        try:
            session = self.session_manager.load_session(session_id)
            if not session:
                return
            
            new_title, ok = QInputDialog.getText(
                self, "重命名会话", "请输入新的会话名称:", 
                text=session.title
            )
            
            if ok and new_title.strip():
                session.title = new_title.strip()
                self.session_manager._save_session(session)
                self._refresh_sessions()
                
        except Exception as e:
            logger.error(f"重命名会话失败: {e}")
    
    def _export_session(self, session_id: str):
        """导出会话"""
        try:
            session = self.session_manager.load_session(session_id)
            if not session:
                return
            
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出会话", f"{session.title}.md", 
                "Markdown files (*.md);;All files (*.*)"
            )
            
            if file_path:
                success = self.session_manager.export_session(session_id, Path(file_path))
                if success:
                    QMessageBox.information(self, "成功", "会话导出成功！")
                else:
                    QMessageBox.warning(self, "失败", "会话导出失败！")
                    
        except Exception as e:
            logger.error(f"导出会话失败: {e}")
    
    def _delete_session(self, session_id: str):
        """删除会话"""
        try:
            reply = QMessageBox.question(
                self, "确认删除", "确定要删除这个会话吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                success = self.session_manager.delete_session(session_id)
                if success:
                    self.session_deleted.emit(session_id)
                    self._refresh_sessions()
                    
        except Exception as e:
            logger.error(f"删除会话失败: {e}")
    
    def refresh(self):
        """公共刷新方法"""
        self._refresh_sessions()


class EnhancedQAInterface(QWidget):
    """增强版问答界面"""
    preview_requested = pyqtSignal(str, int)  # 文件路径, 页码
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.session_manager = get_session_manager()
        self.search_system = None
        self.query_thread = None
        
        self._init_ui()
        self._init_search_system()
        self._load_current_session()
    
    def _init_ui(self):
        """初始化界面"""
        try:
            layout = QVBoxLayout(self)
            layout.setContentsMargins(10, 10, 10, 10)
            layout.setSpacing(10)

            # 直接创建对话区域（会话管理已移到左侧导航栏）
            self._create_chat_panel(layout)

        except Exception as e:
            logger.error(f"增强问答界面初始化失败: {e}")
    

    
    def _create_chat_panel(self, parent_layout):
        """创建对话面板"""
        try:
            logger.info("开始创建对话面板")

            # 创建主分割器：聊天区域 + 预览区域
            main_splitter = QSplitter(Qt.Orientation.Horizontal)
            logger.info("创建主分割器成功")

            # 左侧聊天区域
            chat_widget = QWidget()
            chat_layout = QVBoxLayout(chat_widget)
            chat_layout.setContentsMargins(0, 0, 0, 0)

            # 聊天消息滚动区域
            self.chat_scroll = QScrollArea()
            self.chat_scroll.setWidgetResizable(True)
            self.chat_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
            self.chat_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
            self.chat_scroll.setStyleSheet("""
                QScrollArea {
                    border: 1px solid #e0e6ed;
                    border-radius: 4px;
                    background-color: white;
                }
            """)

            # 聊天消息容器
            self.chat_container = QWidget()
            self.chat_container.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
            self.chat_container.customContextMenuRequested.connect(self._show_chat_context_menu)
            self.chat_layout = QVBoxLayout(self.chat_container)
            self.chat_layout.setContentsMargins(10, 10, 10, 10)
            self.chat_layout.setSpacing(15)
            self.chat_layout.addStretch()  # 添加弹性空间，让消息从底部开始

            self.chat_scroll.setWidget(self.chat_container)
            chat_layout.addWidget(self.chat_scroll)

            # 输入区域
            input_layout = QHBoxLayout()
            input_layout.setSpacing(10)

            self.input_text = QTextEdit()
            self.input_text.setPlaceholderText("请输入您的问题...")
            self.input_text.setFixedHeight(45)  # 调整输入框高度稍微高一点
            self.input_text.setMaximumHeight(45)  # 设置最大高度
            self.input_text.setStyleSheet("""
                QTextEdit {
                    border: 2px solid #e0e6ed;
                    border-radius: 10px;
                    padding: 8px 15px;
                    font-size: 11pt;
                    background-color: white;
                }
                QTextEdit:focus {
                    border-color: #1a5099;
                }
            """)

            # 添加键盘事件处理
            self.input_text.keyPressEvent = self._input_key_press_event

            # 设置输入框中文右键菜单
            self.input_text.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
            self.input_text.customContextMenuRequested.connect(self._show_input_context_menu)

            input_layout.addWidget(self.input_text)

            self.send_btn = QPushButton("发送")
            self.send_btn.clicked.connect(self._send_message)
            self.send_btn.setFixedSize(80, 40)
            self.send_btn.setStyleSheet("""
                QPushButton {
                    background-color: #1a5099;
                    color: white;
                    border: none;
                    border-radius: 20px;
                    font-size: 11pt;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #0d3d73;
                }
                QPushButton:disabled {
                    background-color: #cccccc;
                }
            """)
            input_layout.addWidget(self.send_btn)

            clear_btn = QPushButton("清空")
            clear_btn.clicked.connect(self._clear_session)
            clear_btn.setFixedSize(60, 40)
            clear_btn.setStyleSheet("""
                QPushButton {
                    background-color: #f8f9fa;
                    color: #666;
                    border: 1px solid #e0e6ed;
                    border-radius: 20px;
                    font-size: 10pt;
                }
                QPushButton:hover {
                    background-color: #e9ecef;
                }
            """)
            input_layout.addWidget(clear_btn)

            chat_layout.addLayout(input_layout)

            main_splitter.addWidget(chat_widget)
            logger.info("聊天区域创建成功")

            # 右侧预览区域
            preview_widget = QWidget()
            preview_layout = QVBoxLayout(preview_widget)
            preview_layout.setContentsMargins(0, 0, 0, 0)
            preview_layout.setSpacing(2)  # 设置很小的间距



            # 文档预览区域
            self.document_preview = PreviewWidget()
            # 设置预览区域的最小宽度，适应A4文档显示，移除最大宽度限制以支持自适应
            self.document_preview.setMinimumWidth(400) 
            preview_layout.addWidget(self.document_preview)

            main_splitter.addWidget(preview_widget)
            logger.info("预览区域创建成功")

            # 设置分割器比例 (聊天区域:预览区域 = 1:1，两个区域宽度相等)
            main_splitter.setSizes([500, 500])
            # 设置分割器的拉伸因子，两个区域平等扩展
            main_splitter.setStretchFactor(0, 1)  # 聊天区域
            main_splitter.setStretchFactor(1, 1)  # 预览区域

            parent_layout.addWidget(main_splitter)
            logger.info("主分割器添加到布局成功")



        except Exception as e:
            logger.error(f"对话面板创建失败: {e}")

    def _input_key_press_event(self, event):
        """输入框键盘事件处理"""
        try:
            from PyQt6.QtCore import Qt
            from PyQt6.QtGui import QKeyEvent

            # Ctrl+Enter 或 Enter 发送消息
            if event.key() == Qt.Key.Key_Return or event.key() == Qt.Key.Key_Enter:
                if event.modifiers() == Qt.KeyboardModifier.ControlModifier:
                    # Ctrl+Enter 插入换行
                    QTextEdit.keyPressEvent(self.input_text, event)
                else:
                    # Enter 发送消息
                    self._send_message()
            else:
                # 其他按键正常处理
                QTextEdit.keyPressEvent(self.input_text, event)

        except Exception as e:
            logger.error(f"输入框键盘事件处理失败: {e}")
            # 回退到默认处理
            QTextEdit.keyPressEvent(self.input_text, event)

    def _add_message_to_chat(self, message, is_user=True, sources=None):
        """添加消息到聊天界面"""
        try:
            # 创建消息容器
            message_widget = QWidget()
            message_layout = QHBoxLayout(message_widget)
            message_layout.setContentsMargins(0, 0, 0, 0)

            if is_user:
                # 用户消息 - 右对齐
                message_layout.addStretch()

                message_frame = QFrame()
                message_frame.setStyleSheet("""
                    QFrame {
                        background-color: #1a5099;
                        border-radius: 12px;
                        padding: 8px;
                    }
                """)
                # 设置消息框的最大宽度和大小策略
                message_frame.setMaximumWidth(600)  # 调整最大宽度为600px
                message_frame.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Minimum)
                # 用户消息不设置右键菜单
                message_frame.setContextMenuPolicy(Qt.ContextMenuPolicy.NoContextMenu)

                frame_layout = QVBoxLayout(message_frame)
                frame_layout.setContentsMargins(5, 1, 5, 1)
                message_label = QLabel(message)
                message_label.setWordWrap(True)
                message_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
                message_label.setStyleSheet("color: white; font-size: 11pt; line-height: 1.2;")
                # 设置标签的大小策略，让它能够根据内容调整
                message_label.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Minimum)
                message_label.setMinimumWidth(400)
                # 为用户消息标签设置中文右键菜单
                message_label.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
                message_label.customContextMenuRequested.connect(lambda pos, label=message_label: self._show_user_message_context_menu(pos, label))
                frame_layout.addWidget(message_label)

                message_layout.addWidget(message_frame)

            else:
                # AI回答 - 左对齐
                message_frame = QFrame()
                message_frame.setStyleSheet("""
                    QFrame {
                        background-color: #f8f9fa;
                        border: 1px solid #e0e6ed;
                        border-radius: 12px;
                        padding: 8px;
                        max-width: 400px;
                    }
                """)
                # 为AI回答的消息框架也设置右键菜单
                message_frame.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
                message_frame.customContextMenuRequested.connect(lambda pos, frame=message_frame: self._show_frame_context_menu(pos, frame))

                frame_layout = QVBoxLayout(message_frame)
                frame_layout.setContentsMargins(12, 6, 12, 6)

                # AI回答内容
                message_label = QLabel()
                message_label.setWordWrap(True)
                message_label.setTextInteractionFlags(
                    Qt.TextInteractionFlag.TextSelectableByMouse |
                    Qt.TextInteractionFlag.LinksAccessibleByMouse
                )
                message_label.setStyleSheet("color: #333; font-size: 11pt; line-height: 1.4;")
                # 设置自定义右键菜单
                message_label.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
                message_label.customContextMenuRequested.connect(lambda pos, label=message_label: self._show_message_context_menu(pos, label))

                # 处理带链接的消息
                if sources:
                    formatted_message = self._format_message_with_sources(message, sources)
                    message_label.setText(formatted_message)
                    message_label.setTextFormat(Qt.TextFormat.RichText)  # 启用富文本
                    message_label.setOpenExternalLinks(False)
                    # 确保每次都重新连接信号，避免重复连接
                    try:
                        message_label.linkActivated.disconnect()
                    except:
                        pass
                    message_label.linkActivated.connect(self._on_source_link_clicked)
                    print(f"🔗 DEBUG: 为消息标签连接了链接点击信号，来源数量: {len(sources)}")  # 调试信息

                    # 测试链接是否可点击
                    message_label.setToolTip("点击来源链接可预览文档")
                    logger.info(f"🔗 已为消息标签设置链接点击处理，来源数量: {len(sources)}")
                else:
                    message_label.setText(message)
                    message_label.setTextFormat(Qt.TextFormat.PlainText)

                frame_layout.addWidget(message_label)

                message_layout.addWidget(message_frame)
                message_layout.addStretch()

            # 插入到聊天布局中（在stretch之前）
            self.chat_layout.insertWidget(self.chat_layout.count() - 1, message_widget)

            # 滚动到底部
            QTimer.singleShot(100, self._scroll_to_bottom)

        except Exception as e:
            logger.error(f"添加消息失败: {e}")

    def _format_message_with_sources(self, message, sources):
        """格式化带来源链接的消息"""
        try:
            formatted_message = message

            if not sources:
                return formatted_message

            # 添加参考来源部分
            formatted_message += "<br><br><b>📚 参考来源：</b><br>"

            # 为每个来源添加链接
            for i, source in enumerate(sources, 1):
                # 处理来源数据（可能是字典或对象）
                if isinstance(source, dict):
                    file_name = source.get('file_name', '未知文件')
                    content = source.get('content', '')
                    page_number = source.get('page_number')
                else:
                    file_name = getattr(source, 'file_name', '未知文件')
                    content = getattr(source, 'content', '')
                    page_number = getattr(source, 'page_number', None)

                # 创建制度名称（去掉.pdf后缀）
                policy_name = file_name.replace('.pdf', '') if file_name.endswith('.pdf') else file_name

                # 创建引用来源（内容的前50个字符）
                quote_text = content[:50] + "..." if len(content) > 50 else content

                # 页码信息
                page_info = f" (第{page_number}页)" if page_number else ""

                # 创建来源链接 - 制度名称 + 引用来源，添加首行缩进
                source_link = f'<p style="text-indent: 2em; margin: 0;"><a href="source_{i}" style="color: #1a5099; text-decoration: underline; font-weight: bold;">[{i}] {policy_name}{page_info}</a></p>'
                source_quote = f'<div style="margin-left: 20px; color: #666; font-size: 10pt; font-style: italic;">"{quote_text}"</div>'

                if i > 1:
                    formatted_message += "<br>"
                formatted_message += source_link + source_quote

            return formatted_message

        except Exception as e:
            logger.error(f"格式化消息失败: {e}")
            return message

    def _on_source_link_clicked(self, link):
        """处理来源链接点击"""
        try:
            logger.info(f"🔗 来源链接点击: {link}")
            print(f"🔗 DEBUG: 链接点击 - {link}")  # 添加控制台输出用于调试

            # 强制刷新日志
            import sys
            if sys.stdout is not None:
                sys.stdout.flush()

            if link.startswith("source_"):
                source_index = int(link.split("_")[1]) - 1
                logger.info(f"📍 解析来源索引: {source_index}")
                print(f"📍 DEBUG: 来源索引 - {source_index}")

                session = self.session_manager.get_current_session()

                if session and session.messages:
                    logger.info(f"📝 当前会话消息数量: {len(session.messages)}")
                    print(f"📝 DEBUG: 会话消息数 - {len(session.messages)}")

                    # 获取最后一条AI消息的来源
                    for message in reversed(session.messages):
                        if hasattr(message, 'type') and message.type == 'assistant' and hasattr(message, 'sources') and message.sources:
                            logger.info(f"✅ 找到AI消息，来源数量: {len(message.sources)}")
                            print(f"✅ DEBUG: AI消息来源数 - {len(message.sources)}")

                            if 0 <= source_index < len(message.sources):
                                source = message.sources[source_index]
                                logger.info(f"🎯 显示来源 {source_index + 1}: {type(source)}")
                                print(f"🎯 DEBUG: 显示来源 - {source_index + 1}")
                                print(f"🎯 DEBUG: 来源数据 - {source}")
                                self._show_document_preview(source)
                                return  # 成功处理，直接返回
                            else:
                                logger.warning(f"❌ 来源索引超出范围: {source_index}, 总数: {len(message.sources)}")
                                print(f"❌ DEBUG: 索引超出范围 - {source_index}/{len(message.sources)}")
                                self.document_preview.setHtml(f"<b>来源索引超出范围：</b>{source_index}/{len(message.sources)}")
                            break
                    else:
                        logger.warning("❌ 未找到包含来源的AI消息")
                        print("❌ DEBUG: 未找到AI消息")
                        self.document_preview.setHtml("<b>未找到包含来源的AI消息</b>")
                else:
                    logger.warning("❌ 当前会话为空或无消息")
                    print("❌ DEBUG: 会话为空")
                    self.document_preview.setHtml("<b>当前会话为空或无消息</b>")
            else:
                logger.warning(f"❌ 无效的链接格式: {link}")
                print(f"❌ DEBUG: 无效链接 - {link}")
                self.document_preview.setHtml(f"<b>无效的链接格式：</b>{link}")

        except Exception as e:
            logger.error(f"❌ 处理来源链接点击失败: {e}")
            print(f"❌ DEBUG: 链接处理异常 - {e}")
            import traceback
            traceback.print_exc()
            # 显示错误信息到预览区域
            error_text = f"链接处理失败: {str(e)}\n\n点击的链接: {link}"
            if hasattr(self.document_preview, 'setHtml'):
                self.document_preview.setHtml(error_text)
            else:
                self.document_preview.setPlainText(error_text)

    def _show_document_preview(self, source):
        """在预览区域显示文档内容"""
        try:
            if isinstance(source, dict):
                source_file = source.get('source_file', '未知文件')
                page_number = source.get('page_number', 1)
            else:
                source_file = getattr(source, 'source_file', '未知文件')
                page_number = getattr(source, 'page_number', 1)
            # 调用 PreviewWidget 的加载方法
            self.document_preview.load_document(source_file, page_number)
        except Exception as e:
            logger.error(f"文档预览失败: {e}")
            if hasattr(self.document_preview, 'setHtml'):
                self.document_preview.setHtml(f"<b>文档预览失败：</b>{e}")
            else:
                self.document_preview.setPlainText(f"文档预览失败: {e}")

    def _scroll_to_bottom(self):
        """滚动到聊天区域底部"""
        try:
            scrollbar = self.chat_scroll.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())
        except Exception as e:
            logger.error(f"滚动失败: {e}")

    def _show_chat_context_menu(self, position):
        """显示聊天区域右键菜单"""
        try:
            menu = QMenu(self.chat_container)

            # 复制选中文本
            copy_action = QAction("复制", self)
            copy_action.triggered.connect(self._copy_selected_text)
            menu.addAction(copy_action)

            # 全选
            select_all_action = QAction("全选", self)
            select_all_action.triggered.connect(self._select_all_text)
            menu.addAction(select_all_action)

            # 显示菜单
            menu.exec(self.chat_container.mapToGlobal(position))

        except Exception as e:
            logger.error(f"显示聊天右键菜单失败: {e}")

    def _show_message_context_menu(self, position, message_label):
        """显示消息标签的自定义右键菜单"""
        try:
            menu = QMenu(message_label)

            # 复制
            copy_action = QAction("复制", self)
            copy_action.triggered.connect(lambda: self._copy_message_text(message_label))
            menu.addAction(copy_action)

            # 全选
            select_all_action = QAction("全选", self)
            select_all_action.triggered.connect(lambda: self._select_all_message_text(message_label))
            menu.addAction(select_all_action)

            # 显示菜单
            menu.exec(message_label.mapToGlobal(position))

        except Exception as e:
            logger.error(f"显示消息右键菜单失败: {e}")

    def _show_user_message_context_menu(self, position, message_label):
        """显示用户消息标签的自定义中文右键菜单"""
        try:
            menu = QMenu(self)  # 使用self作为父组件而不是message_label

            # 复制
            copy_action = QAction("复制", self)
            copy_action.triggered.connect(lambda: self._copy_message_text(message_label))
            menu.addAction(copy_action)

            # 全选
            select_all_action = QAction("全选", self)
            select_all_action.triggered.connect(lambda: self._select_all_message_text(message_label))
            menu.addAction(select_all_action)

            # 显示菜单
            menu.exec(message_label.mapToGlobal(position))

        except Exception as e:
            logger.error(f"显示用户消息右键菜单失败: {e}")

    def _show_input_context_menu(self, position):
        """显示输入框的自定义中文右键菜单"""
        try:
            menu = QMenu(self)

            # 剪切
            cut_action = QAction("剪切", self)
            cut_action.triggered.connect(self.input_text.cut)
            cut_action.setEnabled(self.input_text.textCursor().hasSelection())
            menu.addAction(cut_action)

            # 复制
            copy_action = QAction("复制", self)
            copy_action.triggered.connect(self.input_text.copy)
            copy_action.setEnabled(self.input_text.textCursor().hasSelection())
            menu.addAction(copy_action)

            # 粘贴
            paste_action = QAction("粘贴", self)
            paste_action.triggered.connect(self.input_text.paste)
            # 检查剪贴板是否有内容
            from PyQt6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            paste_action.setEnabled(bool(clipboard.text()))
            menu.addAction(paste_action)

            menu.addSeparator()

            # 全选
            select_all_action = QAction("全选", self)
            select_all_action.triggered.connect(self.input_text.selectAll)
            select_all_action.setEnabled(bool(self.input_text.toPlainText()))
            menu.addAction(select_all_action)

            # 显示菜单
            menu.exec(self.input_text.mapToGlobal(position))

        except Exception as e:
            logger.error(f"显示输入框右键菜单失败: {e}")

    def _show_frame_context_menu(self, position, message_frame):
        """显示消息框架的自定义右键菜单"""
        try:
            # 找到框架内的标签
            message_label = None
            for child in message_frame.findChildren(QLabel):
                message_label = child
                break

            if message_label:
                menu = QMenu(message_frame)

                # 复制
                copy_action = QAction("复制", self)
                copy_action.triggered.connect(lambda: self._copy_message_text(message_label))
                menu.addAction(copy_action)

                # 全选
                select_all_action = QAction("全选", self)
                select_all_action.triggered.connect(lambda: self._select_all_message_text(message_label))
                menu.addAction(select_all_action)

                # 显示菜单
                menu.exec(message_frame.mapToGlobal(position))

        except Exception as e:
            logger.error(f"显示框架右键菜单失败: {e}")

    def _copy_message_text(self, message_label):
        """复制消息标签的选中文本或全部文本"""
        try:
            from PyQt6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()

            if message_label.hasSelectedText():
                clipboard.setText(message_label.selectedText())
            else:
                # 如果没有选中文本，复制整个消息内容
                clipboard.setText(message_label.text())

        except Exception as e:
            logger.error(f"复制消息文本失败: {e}")

    def _select_all_message_text(self, message_label):
        """全选消息标签的文本（复制到剪贴板）"""
        try:
            # QLabel没有selectAll方法，我们直接复制全部文本到剪贴板
            from PyQt6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            text = message_label.text()
            if text:
                clipboard.setText(text)
                logger.info("已复制全部消息文本到剪贴板")
        except Exception as e:
            logger.error(f"全选消息文本失败: {e}")

    def _copy_selected_text(self):
        """复制选中的文本"""
        try:
            from PyQt6.QtWidgets import QApplication
            # 获取当前选中的文本
            clipboard = QApplication.clipboard()

            # 遍历所有消息标签，查找选中的文本
            selected_text = ""
            for i in range(self.chat_layout.count()):
                widget = self.chat_layout.itemAt(i).widget()
                if widget and hasattr(widget, 'findChildren'):
                    labels = widget.findChildren(QLabel)
                    for label in labels:
                        if label.hasSelectedText():
                            selected_text += label.selectedText() + "\n"

            if selected_text:
                clipboard.setText(selected_text.strip())
            else:
                # 如果没有选中文本，复制所有聊天内容
                self._copy_all_chat()

        except Exception as e:
            logger.error(f"复制文本失败: {e}")

    def _select_all_text(self):
        """全选聊天内容"""
        try:
            # 遍历所有消息标签，选中所有文本
            for i in range(self.chat_layout.count()):
                widget = self.chat_layout.itemAt(i).widget()
                if widget and hasattr(widget, 'findChildren'):
                    labels = widget.findChildren(QLabel)
                    for label in labels:
                        if hasattr(label, 'selectAll'):
                            label.selectAll()

        except Exception as e:
            logger.error(f"全选文本失败: {e}")

    def _copy_all_chat(self):
        """复制所有聊天内容"""
        try:
            from PyQt6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()

            all_text = ""
            session = self.session_manager.get_current_session()
            if session and session.messages:
                for message in session.messages:
                    if hasattr(message, 'type'):
                        if message.type == 'user':
                            all_text += f"用户: {message.content}\n\n"
                        elif message.type == 'assistant':
                            all_text += f"AI助手: {message.content}\n\n"

            clipboard.setText(all_text.strip())

        except Exception as e:
            logger.error(f"复制所有聊天内容失败: {e}")
    
    def _init_search_system(self):
        """初始化搜索系统"""
        try:
            self.search_system = get_search_system()
        except Exception as e:
            logger.error(f"搜索系统初始化失败: {e}")
    
    def _load_current_session(self):
        """加载当前会话"""
        try:
            session = self.session_manager.get_current_session()
            if session:
                self._display_session_messages(session)
        except Exception as e:
            logger.error(f"加载当前会话失败: {e}")
    
    def _display_session_messages(self, session: ChatSession):
        """显示会话消息"""
        try:
            # 清空现有消息
            self._clear_chat_messages()

            # 重置文档预览区域
            self._reset_document_preview()

            if not session.messages:
                # 显示欢迎消息
                welcome_message = """🤖 AI智能助手

欢迎使用公司制度智能问答系统！

您可以询问关于公司制度、规定、流程等相关问题。"""
                self._add_message_to_chat(welcome_message, is_user=False)
                return

            for message in session.messages:
                if message.type == 'user':
                    self._add_message_to_chat(message.content, is_user=True)
                else:
                    # AI消息可能包含来源信息
                    sources = getattr(message, 'sources', None)
                    self._add_message_to_chat(message.content, is_user=False, sources=sources)

        except Exception as e:
            logger.error(f"显示会话消息失败: {e}")

    def _clear_chat_messages(self):
        """清空聊天消息"""
        try:
            # 删除所有消息widget（保留stretch）
            while self.chat_layout.count() > 1:
                child = self.chat_layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
        except Exception as e:
            logger.error(f"清空聊天消息失败: {e}")

    def _reset_document_preview(self):
        """重置文档预览区域"""
        try:
            if hasattr(self, 'document_preview'):
                # 显示默认的欢迎信息
                self.document_preview.show_welcome()
                logger.info("文档预览区域已重置")
        except Exception as e:
            logger.error(f"重置文档预览区域失败: {e}")
    

    
    def _send_message(self):
        """发送消息"""
        try:
            # 检查输入框类型并获取文本
            if hasattr(self.input_text, 'toPlainText'):
                query = self.input_text.toPlainText().strip()
            else:
                query = self.input_text.text().strip()

            if not query:
                return

            # 禁用发送按钮
            self.send_btn.setEnabled(False)
            self.send_btn.setText("处理中...")

            # 添加用户消息
            self.session_manager.add_message('user', query)
            self._add_message_to_chat(query, is_user=True)

            # 清空输入框
            self.input_text.clear()
            
            # 启动AI查询
            self.query_thread = AIQueryThread(query)
            self.query_thread.result_ready.connect(self._on_result_ready)
            self.query_thread.error_occurred.connect(self._on_error_occurred)
            self.query_thread.start()
            
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            self._reset_send_button()
    
    def _on_result_ready(self, result: dict):
        """AI结果就绪"""
        try:
            answer = result.get('answer', '抱歉，无法生成回答。')
            sources = result.get('sources', [])

            # 添加AI消息到会话
            self.session_manager.add_message('assistant', answer, sources)

            # 显示AI回复（带引用）
            self._add_message_to_chat(answer, is_user=False, sources=sources)

            # 刷新会话列表（可能标题已更新）
            if hasattr(self, 'main_window') and hasattr(self.main_window, '_refresh_session_tree'):
                self.main_window._refresh_session_tree()

        except Exception as e:
            logger.error(f"处理AI结果失败: {e}")
        finally:
            self._reset_send_button()
    
    def _on_error_occurred(self, error: str):
        """AI查询错误"""
        try:
            error_msg = f"抱歉，处理您的问题时出现错误：{error}"
            self.session_manager.add_message('assistant', error_msg)
            self._add_message_to_chat(error_msg, is_user=False)
        except Exception as e:
            logger.error(f"处理AI错误失败: {e}")
        finally:
            self._reset_send_button()
    

    
    def _clear_session(self):
        """清空当前会话"""
        try:
            reply = QMessageBox.question(
                self, "确认清空", "确定要清空当前会话吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                self.session_manager.clear_current_session()
                session = self.session_manager.get_current_session()
                self._display_session_messages(session)
                
        except Exception as e:
            logger.error(f"清空会话失败: {e}")
    


    def _reset_send_button(self):
        """重置发送按钮"""
        self.send_btn.setEnabled(True)
        self.send_btn.setText("发送")

    def refresh_session_display(self):
        """刷新会话显示（供外部调用）"""
        try:
            self._load_current_session()
        except Exception as e:
            logger.error(f"刷新会话显示失败: {e}")
