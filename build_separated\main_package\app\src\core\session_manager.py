"""
会话管理模块
管理问答会话的创建、保存、加载和删除
"""
import json
import uuid
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict

from ..utils.config import get_config
from ..utils.logger import get_logger
from ..utils.helpers import ensure_dir, safe_filename

logger = get_logger(__name__)


@dataclass
class ChatMessage:
    """聊天消息数据结构"""
    id: str
    type: str  # 'user' 或 'assistant'
    content: str
    timestamp: str
    sources: List[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChatMessage':
        """从字典创建"""
        return cls(**data)


@dataclass
class ChatSession:
    """聊天会话数据结构"""
    id: str
    title: str
    created_at: str
    updated_at: str
    messages: List[ChatMessage]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'title': self.title,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'messages': [msg.to_dict() for msg in self.messages]
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChatSession':
        """从字典创建"""
        messages = [ChatMessage.from_dict(msg) for msg in data.get('messages', [])]
        return cls(
            id=data['id'],
            title=data['title'],
            created_at=data['created_at'],
            updated_at=data['updated_at'],
            messages=messages
        )


class SessionManager:
    """会话管理器"""
    
    def __init__(self):
        self.config = get_config()
        self.sessions_dir = Path(self.config.data_dir) / "sessions"
        self.current_session: Optional[ChatSession] = None
        
        # 确保会话目录存在
        ensure_dir(self.sessions_dir)
        
        # 加载或创建默认会话
        self._load_or_create_default_session()
    
    def _load_or_create_default_session(self):
        """加载或创建默认会话"""
        try:
            sessions = self.list_sessions()
            if sessions:
                # 加载最近的会话
                latest_session = max(sessions, key=lambda s: s.updated_at)
                self.current_session = latest_session
                logger.info(f"加载最近会话: {latest_session.title}")
            else:
                # 创建新会话
                self.current_session = self.create_new_session("新对话")
                logger.info("创建默认会话")
        except Exception as e:
            logger.error(f"加载默认会话失败: {e}")
            self.current_session = self.create_new_session("新对话")
    
    def create_new_session(self, title: str = None) -> ChatSession:
        """创建新会话"""
        try:
            session_id = str(uuid.uuid4())
            now = datetime.now().isoformat()
            
            if not title:
                title = f"对话 {datetime.now().strftime('%m-%d %H:%M')}"
            
            session = ChatSession(
                id=session_id,
                title=title,
                created_at=now,
                updated_at=now,
                messages=[]
            )
            
            # 保存会话
            self._save_session(session)

            # 设置为当前会话
            self.current_session = session

            logger.info(f"创建新会话: {title}")
            return session
            
        except Exception as e:
            logger.error(f"创建会话失败: {e}")
            raise
    
    def add_message(self, message_type: str, content: str, sources: List[Dict[str, Any]] = None) -> ChatMessage:
        """添加消息到当前会话"""
        try:
            if not self.current_session:
                self.current_session = self.create_new_session()
            
            message_id = str(uuid.uuid4())
            timestamp = datetime.now().isoformat()
            
            message = ChatMessage(
                id=message_id,
                type=message_type,
                content=content,
                timestamp=timestamp,
                sources=sources or []
            )
            
            self.current_session.messages.append(message)
            self.current_session.updated_at = timestamp
            
            # 如果是第一条用户消息，用它作为会话标题
            if message_type == 'user' and len(self.current_session.messages) == 1:
                title = content[:30] + "..." if len(content) > 30 else content
                self.current_session.title = title
            
            # 保存会话
            self._save_session(self.current_session)
            
            return message
            
        except Exception as e:
            logger.error(f"添加消息失败: {e}")
            raise
    
    def get_current_session(self) -> Optional[ChatSession]:
        """获取当前会话"""
        return self.current_session
    
    def switch_session(self, session_id: str) -> bool:
        """切换到指定会话"""
        try:
            session = self.load_session(session_id)
            if session:
                self.current_session = session
                logger.info(f"切换到会话: {session.title}")
                return True
            return False
        except Exception as e:
            logger.error(f"切换会话失败: {e}")
            return False
    
    def delete_session(self, session_id: str) -> bool:
        """删除会话"""
        try:
            session_file = self.sessions_dir / f"{session_id}.json"
            if session_file.exists():
                session_file.unlink()
                
                # 如果删除的是当前会话，创建新会话
                if self.current_session and self.current_session.id == session_id:
                    self.current_session = self.create_new_session()
                
                logger.info(f"删除会话: {session_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"删除会话失败: {e}")
            return False
    
    def list_sessions(self) -> List[ChatSession]:
        """列出所有会话"""
        try:
            sessions = []
            for session_file in self.sessions_dir.glob("*.json"):
                try:
                    session = self._load_session_from_file(session_file)
                    if session:
                        sessions.append(session)
                except Exception as e:
                    logger.warning(f"加载会话文件失败 {session_file}: {e}")
                    continue
            
            # 按更新时间排序
            sessions.sort(key=lambda s: s.updated_at, reverse=True)
            return sessions
            
        except Exception as e:
            logger.error(f"列出会话失败: {e}")
            return []
    
    def load_session(self, session_id: str) -> Optional[ChatSession]:
        """加载指定会话"""
        try:
            session_file = self.sessions_dir / f"{session_id}.json"
            return self._load_session_from_file(session_file)
        except Exception as e:
            logger.error(f"加载会话失败: {e}")
            return None
    
    def _save_session(self, session: ChatSession):
        """保存会话到文件"""
        try:
            session_file = self.sessions_dir / f"{session.id}.json"
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session.to_dict(), f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存会话失败: {e}")
            raise
    
    def _load_session_from_file(self, session_file: Path) -> Optional[ChatSession]:
        """从文件加载会话"""
        try:
            if not session_file.exists():
                return None
            
            with open(session_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return ChatSession.from_dict(data)
            
        except Exception as e:
            logger.error(f"从文件加载会话失败 {session_file}: {e}")
            return None
    
    def clear_current_session(self):
        """清空当前会话"""
        try:
            if self.current_session:
                self.current_session.messages.clear()
                self.current_session.updated_at = datetime.now().isoformat()
                self._save_session(self.current_session)
                logger.info("清空当前会话")
        except Exception as e:
            logger.error(f"清空会话失败: {e}")
    
    def export_session(self, session_id: str, export_path: Path) -> bool:
        """导出会话"""
        try:
            session = self.load_session(session_id)
            if not session:
                return False
            
            # 生成Markdown格式
            markdown_content = f"# {session.title}\n\n"
            markdown_content += f"创建时间: {session.created_at}\n"
            markdown_content += f"更新时间: {session.updated_at}\n\n"
            
            for message in session.messages:
                if message.type == 'user':
                    markdown_content += f"## 用户\n{message.content}\n\n"
                else:
                    markdown_content += f"## AI助手\n{message.content}\n\n"
                    if message.sources:
                        markdown_content += "### 参考来源\n"
                        for source in message.sources:
                            markdown_content += f"- {source.get('file_name', '未知文件')}\n"
                        markdown_content += "\n"
            
            with open(export_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            logger.info(f"导出会话成功: {export_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出会话失败: {e}")
            return False


# 全局会话管理器实例
_session_manager_instance = None


def get_session_manager() -> SessionManager:
    """获取会话管理器实例（单例模式）"""
    global _session_manager_instance
    if _session_manager_instance is None:
        _session_manager_instance = SessionManager()
    return _session_manager_instance
