#pragma once

// @generated by torchgen/gen.py from Functions.h

#ifdef TORCH_ASSERT_NO_OPERATORS
#error This change adds a dependency on native_functions.yaml,            \
  meaning the file will need to be re-compiled every time an operator     \
  is changed or added. Consider if your change would be better placed in  \
  another file, or if a more specific header might achieve the same goal. \
  See NOTE: [Tensor vs. TensorBase]
#endif

#if defined(AT_PER_OPERATOR_HEADERS) && defined(TORCH_ASSERT_ONLY_METHOD_OPERATORS)
#error This change adds a dependency on all pytorch operators, meaning the     \
  file will need to be re-compiled every time an operator is changed or added. \
  Consider including a specific operator from <ATen/ops/{my_operator}.h> and   \
  see NOTE [TORCH_ASSERT_ONLY_METHOD_OPERATORS].
#endif

// NOTE: [TORCH_ASSERT_ONLY_METHOD_OPERATORS]
//
// In ATen, certain generated headers files include the definitions of
// every single operator in PyTorch. Unfortunately this means every
// time an operator signature is updated or changed in
// native_functions.yaml, you (and every other PyTorch developer) need
// to recompile every source file that includes any of these headers.
//
// To break up these header dependencies, and improve incremental
// build times for all PyTorch developers. These headers are split
// into per-operator headers in the `ATen/ops` folder. This limits
// incremental builds to only changes to methods of `Tensor`, or files
// that use the specific operator being changed. With `at::sum` as an
// example, you should include
//
//   <ATen/ops/sum.h>               // instead of ATen/Functions.h
//   <ATen/ops/sum_native.h>        // instead of ATen/NativeFunctions.h
//   <ATen/ops/sum_ops.h>           // instead of ATen/Operators.h
//   <ATen/ops/sum_cpu_dispatch.h>  // instead of ATen/CPUFunctions.h
//
// However, even if you're careful to use this in your own code.
// `Functions.h` might be included indirectly through another header
// without you realising. To avoid this, you can add
//
//   #define TORCH_ASSERT_ONLY_METHOD_OPERATORS
//
// to the top of your source file. This way any time the non-specific
// headers are included, the compiler will error out.
//
// Also, be aware that `ops` are not available in all build
// configurations (namely fb-internal) so you must guard these
// includes with `#ifdef AT_PER_OPERATOR_HEADERS`. e.g.
//
//   #ifndef AT_PER_OPERATOR_HEADERS
//   #include <ATen/Functions.h>
//   #else
//   #include <ATen/ops/sum.h>
//   #endif

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <c10/core/SymInt.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>
#include <c10/util/OptionalArrayRef.h>

#include <ATen/ops/from_blob.h>
#include <ATen/ops/tensor.h>

#include <ATen/ops/_adaptive_avg_pool2d.h>
#include <ATen/ops/_adaptive_avg_pool2d_backward.h>
#include <ATen/ops/_adaptive_avg_pool3d.h>
#include <ATen/ops/_adaptive_avg_pool3d_backward.h>
#include <ATen/ops/_add_batch_dim.h>
#include <ATen/ops/_add_relu.h>
#include <ATen/ops/_addmm_activation.h>
#include <ATen/ops/_aminmax.h>
#include <ATen/ops/_amp_foreach_non_finite_check_and_unscale.h>
#include <ATen/ops/_amp_update_scale.h>
#include <ATen/ops/_assert_async.h>
#include <ATen/ops/_assert_tensor_metadata.h>
#include <ATen/ops/_autocast_to_full_precision.h>
#include <ATen/ops/_autocast_to_reduced_precision.h>
#include <ATen/ops/_backward.h>
#include <ATen/ops/_batch_norm_impl_index.h>
#include <ATen/ops/_batch_norm_impl_index_backward.h>
#include <ATen/ops/_cast_Byte.h>
#include <ATen/ops/_cast_Char.h>
#include <ATen/ops/_cast_Double.h>
#include <ATen/ops/_cast_Float.h>
#include <ATen/ops/_cast_Half.h>
#include <ATen/ops/_cast_Int.h>
#include <ATen/ops/_cast_Long.h>
#include <ATen/ops/_cast_Short.h>
#include <ATen/ops/_cdist_backward.h>
#include <ATen/ops/_cdist_forward.h>
#include <ATen/ops/_cholesky_solve_helper.h>
#include <ATen/ops/_choose_qparams_per_tensor.h>
#include <ATen/ops/_coalesce.h>
#include <ATen/ops/_coalesced.h>
#include <ATen/ops/_compute_linear_combination.h>
#include <ATen/ops/_conj.h>
#include <ATen/ops/_conj_copy.h>
#include <ATen/ops/_conj_physical.h>
#include <ATen/ops/_conv_depthwise2d.h>
#include <ATen/ops/_convert_indices_from_coo_to_csr.h>
#include <ATen/ops/_convert_indices_from_csr_to_coo.h>
#include <ATen/ops/_convert_weight_to_int4pack.h>
#include <ATen/ops/_convolution.h>
#include <ATen/ops/_convolution_double_backward.h>
#include <ATen/ops/_convolution_mode.h>
#include <ATen/ops/_copy_from.h>
#include <ATen/ops/_copy_from_and_resize.h>
#include <ATen/ops/_cslt_compress.h>
#include <ATen/ops/_cslt_sparse_mm.h>
#include <ATen/ops/_ctc_loss.h>
#include <ATen/ops/_ctc_loss_backward.h>
#include <ATen/ops/_cudnn_ctc_loss.h>
#include <ATen/ops/_cudnn_init_dropout_state.h>
#include <ATen/ops/_cudnn_rnn.h>
#include <ATen/ops/_cudnn_rnn_backward.h>
#include <ATen/ops/_cudnn_rnn_flatten_weight.h>
#include <ATen/ops/_cufft_clear_plan_cache.h>
#include <ATen/ops/_cufft_get_plan_cache_max_size.h>
#include <ATen/ops/_cufft_get_plan_cache_size.h>
#include <ATen/ops/_cufft_set_plan_cache_max_size.h>
#include <ATen/ops/_cummax_helper.h>
#include <ATen/ops/_cummin_helper.h>
#include <ATen/ops/_debug_has_internal_overlap.h>
#include <ATen/ops/_dimI.h>
#include <ATen/ops/_dimV.h>
#include <ATen/ops/_dim_arange.h>
#include <ATen/ops/_dirichlet_grad.h>
#include <ATen/ops/_efficient_attention_backward.h>
#include <ATen/ops/_efficient_attention_forward.h>
#include <ATen/ops/_efficientzerotensor.h>
#include <ATen/ops/_embedding_bag.h>
#include <ATen/ops/_embedding_bag_backward.h>
#include <ATen/ops/_embedding_bag_dense_backward.h>
#include <ATen/ops/_embedding_bag_forward_only.h>
#include <ATen/ops/_embedding_bag_per_sample_weights_backward.h>
#include <ATen/ops/_embedding_bag_sparse_backward.h>
#include <ATen/ops/_empty_affine_quantized.h>
#include <ATen/ops/_empty_per_channel_affine_quantized.h>
#include <ATen/ops/_euclidean_dist.h>
#include <ATen/ops/_fake_quantize_learnable_per_channel_affine.h>
#include <ATen/ops/_fake_quantize_learnable_per_channel_affine_backward.h>
#include <ATen/ops/_fake_quantize_learnable_per_tensor_affine.h>
#include <ATen/ops/_fake_quantize_learnable_per_tensor_affine_backward.h>
#include <ATen/ops/_fake_quantize_per_tensor_affine_cachemask_tensor_qparams.h>
#include <ATen/ops/_fft_c2c.h>
#include <ATen/ops/_fft_c2r.h>
#include <ATen/ops/_fft_r2c.h>
#include <ATen/ops/_fill_mem_eff_dropout_mask.h>
#include <ATen/ops/_flash_attention_backward.h>
#include <ATen/ops/_flash_attention_forward.h>
#include <ATen/ops/_foobar.h>
#include <ATen/ops/_foreach_abs.h>
#include <ATen/ops/_foreach_acos.h>
#include <ATen/ops/_foreach_add.h>
#include <ATen/ops/_foreach_addcdiv.h>
#include <ATen/ops/_foreach_addcmul.h>
#include <ATen/ops/_foreach_asin.h>
#include <ATen/ops/_foreach_atan.h>
#include <ATen/ops/_foreach_ceil.h>
#include <ATen/ops/_foreach_clamp_max.h>
#include <ATen/ops/_foreach_clamp_min.h>
#include <ATen/ops/_foreach_copy.h>
#include <ATen/ops/_foreach_cos.h>
#include <ATen/ops/_foreach_cosh.h>
#include <ATen/ops/_foreach_div.h>
#include <ATen/ops/_foreach_erf.h>
#include <ATen/ops/_foreach_erfc.h>
#include <ATen/ops/_foreach_exp.h>
#include <ATen/ops/_foreach_expm1.h>
#include <ATen/ops/_foreach_floor.h>
#include <ATen/ops/_foreach_frac.h>
#include <ATen/ops/_foreach_lerp.h>
#include <ATen/ops/_foreach_lgamma.h>
#include <ATen/ops/_foreach_log.h>
#include <ATen/ops/_foreach_log10.h>
#include <ATen/ops/_foreach_log1p.h>
#include <ATen/ops/_foreach_log2.h>
#include <ATen/ops/_foreach_maximum.h>
#include <ATen/ops/_foreach_minimum.h>
#include <ATen/ops/_foreach_mul.h>
#include <ATen/ops/_foreach_neg.h>
#include <ATen/ops/_foreach_norm.h>
#include <ATen/ops/_foreach_pow.h>
#include <ATen/ops/_foreach_reciprocal.h>
#include <ATen/ops/_foreach_round.h>
#include <ATen/ops/_foreach_sigmoid.h>
#include <ATen/ops/_foreach_sign.h>
#include <ATen/ops/_foreach_sin.h>
#include <ATen/ops/_foreach_sinh.h>
#include <ATen/ops/_foreach_sqrt.h>
#include <ATen/ops/_foreach_sub.h>
#include <ATen/ops/_foreach_tan.h>
#include <ATen/ops/_foreach_tanh.h>
#include <ATen/ops/_foreach_trunc.h>
#include <ATen/ops/_foreach_zero.h>
#include <ATen/ops/_functional_assert_async.h>
#include <ATen/ops/_functional_sym_constrain_range.h>
#include <ATen/ops/_functional_sym_constrain_range_for_size.h>
#include <ATen/ops/_fused_adam.h>
#include <ATen/ops/_fused_adamw.h>
#include <ATen/ops/_fused_dropout.h>
#include <ATen/ops/_fused_moving_avg_obs_fq_helper.h>
#include <ATen/ops/_fused_sdp_choice.h>
#include <ATen/ops/_fw_primal.h>
#include <ATen/ops/_fw_primal_copy.h>
#include <ATen/ops/_gather_sparse_backward.h>
#include <ATen/ops/_grid_sampler_2d_cpu_fallback.h>
#include <ATen/ops/_grid_sampler_2d_cpu_fallback_backward.h>
#include <ATen/ops/_has_compatible_shallow_copy_type.h>
#include <ATen/ops/_has_same_storage_numel.h>
#include <ATen/ops/_histogramdd_bin_edges.h>
#include <ATen/ops/_histogramdd_from_bin_cts.h>
#include <ATen/ops/_histogramdd_from_bin_tensors.h>
#include <ATen/ops/_index_put_impl.h>
#include <ATen/ops/_indices.h>
#include <ATen/ops/_indices_copy.h>
#include <ATen/ops/_int_mm.h>
#include <ATen/ops/_is_all_true.h>
#include <ATen/ops/_is_any_true.h>
#include <ATen/ops/_is_zerotensor.h>
#include <ATen/ops/_linalg_check_errors.h>
#include <ATen/ops/_linalg_det.h>
#include <ATen/ops/_linalg_eigh.h>
#include <ATen/ops/_linalg_slogdet.h>
#include <ATen/ops/_linalg_solve_ex.h>
#include <ATen/ops/_linalg_svd.h>
#include <ATen/ops/_local_scalar_dense.h>
#include <ATen/ops/_log_softmax.h>
#include <ATen/ops/_log_softmax_backward_data.h>
#include <ATen/ops/_logcumsumexp.h>
#include <ATen/ops/_lstm_mps.h>
#include <ATen/ops/_lu_with_info.h>
#include <ATen/ops/_make_dep_token.h>
#include <ATen/ops/_make_dual.h>
#include <ATen/ops/_make_dual_copy.h>
#include <ATen/ops/_make_per_channel_quantized_tensor.h>
#include <ATen/ops/_make_per_tensor_quantized_tensor.h>
#include <ATen/ops/_masked_scale.h>
#include <ATen/ops/_masked_softmax.h>
#include <ATen/ops/_masked_softmax_backward.h>
#include <ATen/ops/_mixed_dtypes_linear.h>
#include <ATen/ops/_mkldnn_reshape.h>
#include <ATen/ops/_mkldnn_transpose.h>
#include <ATen/ops/_mps_convolution.h>
#include <ATen/ops/_mps_convolution_transpose.h>
#include <ATen/ops/_native_batch_norm_legit.h>
#include <ATen/ops/_native_batch_norm_legit_no_training.h>
#include <ATen/ops/_native_multi_head_attention.h>
#include <ATen/ops/_neg_view.h>
#include <ATen/ops/_neg_view_copy.h>
#include <ATen/ops/_nested_from_padded.h>
#include <ATen/ops/_nested_from_padded_and_nested_example.h>
#include <ATen/ops/_nested_select_backward.h>
#include <ATen/ops/_nested_sum_backward.h>
#include <ATen/ops/_nested_tensor_from_mask.h>
#include <ATen/ops/_nested_tensor_from_mask_left_aligned.h>
#include <ATen/ops/_nested_tensor_from_tensor_list.h>
#include <ATen/ops/_nested_tensor_size.h>
#include <ATen/ops/_nested_tensor_softmax_with_shape.h>
#include <ATen/ops/_nested_tensor_storage_offsets.h>
#include <ATen/ops/_nested_tensor_strides.h>
#include <ATen/ops/_nested_view_from_buffer.h>
#include <ATen/ops/_nested_view_from_buffer_copy.h>
#include <ATen/ops/_new_zeros_with_same_feature_meta.h>
#include <ATen/ops/_nnpack_available.h>
#include <ATen/ops/_nnpack_spatial_convolution.h>
#include <ATen/ops/_nnz.h>
#include <ATen/ops/_pack_padded_sequence.h>
#include <ATen/ops/_pack_padded_sequence_backward.h>
#include <ATen/ops/_pad_circular.h>
#include <ATen/ops/_pad_enum.h>
#include <ATen/ops/_pad_packed_sequence.h>
#include <ATen/ops/_pdist_backward.h>
#include <ATen/ops/_pdist_forward.h>
#include <ATen/ops/_pin_memory.h>
#include <ATen/ops/_prelu_kernel.h>
#include <ATen/ops/_prelu_kernel_backward.h>
#include <ATen/ops/_propagate_xla_data.h>
#include <ATen/ops/_remove_batch_dim.h>
#include <ATen/ops/_reshape_alias.h>
#include <ATen/ops/_reshape_alias_copy.h>
#include <ATen/ops/_reshape_copy.h>
#include <ATen/ops/_reshape_from_tensor.h>
#include <ATen/ops/_resize_output.h>
#include <ATen/ops/_rowwise_prune.h>
#include <ATen/ops/_sample_dirichlet.h>
#include <ATen/ops/_saturate_weight_to_fp16.h>
#include <ATen/ops/_scaled_dot_product_attention_math.h>
#include <ATen/ops/_scaled_dot_product_efficient_attention.h>
#include <ATen/ops/_scaled_dot_product_efficient_attention_backward.h>
#include <ATen/ops/_scaled_dot_product_flash_attention.h>
#include <ATen/ops/_scaled_dot_product_flash_attention_backward.h>
#include <ATen/ops/_scaled_mm.h>
#include <ATen/ops/_segment_reduce_backward.h>
#include <ATen/ops/_shape_as_tensor.h>
#include <ATen/ops/_slow_conv2d_backward.h>
#include <ATen/ops/_slow_conv2d_forward.h>
#include <ATen/ops/_sobol_engine_draw.h>
#include <ATen/ops/_sobol_engine_ff.h>
#include <ATen/ops/_sobol_engine_initialize_state.h>
#include <ATen/ops/_sobol_engine_scramble.h>
#include <ATen/ops/_softmax.h>
#include <ATen/ops/_softmax_backward_data.h>
#include <ATen/ops/_sparse_addmm.h>
#include <ATen/ops/_sparse_broadcast_to.h>
#include <ATen/ops/_sparse_broadcast_to_copy.h>
#include <ATen/ops/_sparse_bsc_tensor_unsafe.h>
#include <ATen/ops/_sparse_bsr_tensor_unsafe.h>
#include <ATen/ops/_sparse_compressed_tensor_unsafe.h>
#include <ATen/ops/_sparse_coo_tensor_unsafe.h>
#include <ATen/ops/_sparse_coo_tensor_with_dims.h>
#include <ATen/ops/_sparse_coo_tensor_with_dims_and_tensors.h>
#include <ATen/ops/_sparse_csc_tensor_unsafe.h>
#include <ATen/ops/_sparse_csr_prod.h>
#include <ATen/ops/_sparse_csr_sum.h>
#include <ATen/ops/_sparse_csr_tensor_unsafe.h>
#include <ATen/ops/_sparse_log_softmax.h>
#include <ATen/ops/_sparse_log_softmax_backward_data.h>
#include <ATen/ops/_sparse_mask_projection.h>
#include <ATen/ops/_sparse_mm.h>
#include <ATen/ops/_sparse_mm_reduce_impl.h>
#include <ATen/ops/_sparse_mm_reduce_impl_backward.h>
#include <ATen/ops/_sparse_semi_structured_linear.h>
#include <ATen/ops/_sparse_softmax.h>
#include <ATen/ops/_sparse_softmax_backward_data.h>
#include <ATen/ops/_sparse_sparse_matmul.h>
#include <ATen/ops/_sparse_sum.h>
#include <ATen/ops/_sparse_sum_backward.h>
#include <ATen/ops/_spdiags.h>
#include <ATen/ops/_stack.h>
#include <ATen/ops/_standard_gamma.h>
#include <ATen/ops/_standard_gamma_grad.h>
#include <ATen/ops/_test_ambiguous_defaults.h>
#include <ATen/ops/_test_autograd_multiple_dispatch.h>
#include <ATen/ops/_test_autograd_multiple_dispatch_view.h>
#include <ATen/ops/_test_autograd_multiple_dispatch_view_copy.h>
#include <ATen/ops/_test_check_tensor.h>
#include <ATen/ops/_test_functorch_fallback.h>
#include <ATen/ops/_test_optional_filled_intlist.h>
#include <ATen/ops/_test_optional_floatlist.h>
#include <ATen/ops/_test_optional_intlist.h>
#include <ATen/ops/_test_serialization_subcmul.h>
#include <ATen/ops/_test_string_default.h>
#include <ATen/ops/_test_warn_in_autograd.h>
#include <ATen/ops/_thnn_differentiable_gru_cell_backward.h>
#include <ATen/ops/_thnn_differentiable_lstm_cell_backward.h>
#include <ATen/ops/_thnn_fused_gru_cell.h>
#include <ATen/ops/_thnn_fused_gru_cell_backward.h>
#include <ATen/ops/_thnn_fused_lstm_cell.h>
#include <ATen/ops/_thnn_fused_lstm_cell_backward.h>
#include <ATen/ops/_thnn_fused_lstm_cell_backward_impl.h>
#include <ATen/ops/_to_copy.h>
#include <ATen/ops/_to_cpu.h>
#include <ATen/ops/_to_dense.h>
#include <ATen/ops/_to_sparse.h>
#include <ATen/ops/_to_sparse_bsc.h>
#include <ATen/ops/_to_sparse_bsr.h>
#include <ATen/ops/_to_sparse_csc.h>
#include <ATen/ops/_to_sparse_csr.h>
#include <ATen/ops/_to_sparse_semi_structured.h>
#include <ATen/ops/_transform_bias_rescale_qkv.h>
#include <ATen/ops/_transformer_encoder_layer_fwd.h>
#include <ATen/ops/_trilinear.h>
#include <ATen/ops/_triton_multi_head_attention.h>
#include <ATen/ops/_triton_scaled_dot_attention.h>
#include <ATen/ops/_unique.h>
#include <ATen/ops/_unique2.h>
#include <ATen/ops/_unpack_dual.h>
#include <ATen/ops/_unsafe_index.h>
#include <ATen/ops/_unsafe_index_put.h>
#include <ATen/ops/_unsafe_view.h>
#include <ATen/ops/_upsample_bicubic2d_aa.h>
#include <ATen/ops/_upsample_bicubic2d_aa_backward.h>
#include <ATen/ops/_upsample_bilinear2d_aa.h>
#include <ATen/ops/_upsample_bilinear2d_aa_backward.h>
#include <ATen/ops/_upsample_nearest_exact1d.h>
#include <ATen/ops/_upsample_nearest_exact1d_backward.h>
#include <ATen/ops/_upsample_nearest_exact2d.h>
#include <ATen/ops/_upsample_nearest_exact2d_backward.h>
#include <ATen/ops/_upsample_nearest_exact3d.h>
#include <ATen/ops/_upsample_nearest_exact3d_backward.h>
#include <ATen/ops/_use_cudnn_ctc_loss.h>
#include <ATen/ops/_use_cudnn_rnn_flatten_weight.h>
#include <ATen/ops/_validate_compressed_sparse_indices.h>
#include <ATen/ops/_validate_sparse_bsc_tensor_args.h>
#include <ATen/ops/_validate_sparse_bsr_tensor_args.h>
#include <ATen/ops/_validate_sparse_compressed_tensor_args.h>
#include <ATen/ops/_validate_sparse_coo_tensor_args.h>
#include <ATen/ops/_validate_sparse_csc_tensor_args.h>
#include <ATen/ops/_validate_sparse_csr_tensor_args.h>
#include <ATen/ops/_values.h>
#include <ATen/ops/_values_copy.h>
#include <ATen/ops/_version.h>
#include <ATen/ops/_weight_int4pack_mm.h>
#include <ATen/ops/_weight_norm.h>
#include <ATen/ops/_weight_norm_differentiable_backward.h>
#include <ATen/ops/_weight_norm_interface.h>
#include <ATen/ops/_weight_norm_interface_backward.h>
#include <ATen/ops/abs.h>
#include <ATen/ops/absolute.h>
#include <ATen/ops/acos.h>
#include <ATen/ops/acosh.h>
#include <ATen/ops/adaptive_avg_pool1d.h>
#include <ATen/ops/adaptive_avg_pool2d.h>
#include <ATen/ops/adaptive_avg_pool3d.h>
#include <ATen/ops/adaptive_avg_pool3d_backward.h>
#include <ATen/ops/adaptive_max_pool1d.h>
#include <ATen/ops/adaptive_max_pool2d.h>
#include <ATen/ops/adaptive_max_pool2d_backward.h>
#include <ATen/ops/adaptive_max_pool3d.h>
#include <ATen/ops/adaptive_max_pool3d_backward.h>
#include <ATen/ops/add.h>
#include <ATen/ops/addbmm.h>
#include <ATen/ops/addcdiv.h>
#include <ATen/ops/addcmul.h>
#include <ATen/ops/addmm.h>
#include <ATen/ops/addmv.h>
#include <ATen/ops/addr.h>
#include <ATen/ops/adjoint.h>
#include <ATen/ops/affine_grid_generator.h>
#include <ATen/ops/affine_grid_generator_backward.h>
#include <ATen/ops/alias.h>
#include <ATen/ops/alias_copy.h>
#include <ATen/ops/align_as.h>
#include <ATen/ops/align_tensors.h>
#include <ATen/ops/align_to.h>
#include <ATen/ops/all.h>
#include <ATen/ops/allclose.h>
#include <ATen/ops/alpha_dropout.h>
#include <ATen/ops/amax.h>
#include <ATen/ops/amin.h>
#include <ATen/ops/aminmax.h>
#include <ATen/ops/and.h>
#include <ATen/ops/angle.h>
#include <ATen/ops/any.h>
#include <ATen/ops/arange.h>
#include <ATen/ops/arccos.h>
#include <ATen/ops/arccosh.h>
#include <ATen/ops/arcsin.h>
#include <ATen/ops/arcsinh.h>
#include <ATen/ops/arctan.h>
#include <ATen/ops/arctan2.h>
#include <ATen/ops/arctanh.h>
#include <ATen/ops/argmax.h>
#include <ATen/ops/argmin.h>
#include <ATen/ops/argsort.h>
#include <ATen/ops/argwhere.h>
#include <ATen/ops/as_strided.h>
#include <ATen/ops/as_strided_copy.h>
#include <ATen/ops/as_strided_scatter.h>
#include <ATen/ops/asin.h>
#include <ATen/ops/asinh.h>
#include <ATen/ops/atan.h>
#include <ATen/ops/atan2.h>
#include <ATen/ops/atanh.h>
#include <ATen/ops/atleast_1d.h>
#include <ATen/ops/atleast_2d.h>
#include <ATen/ops/atleast_3d.h>
#include <ATen/ops/avg_pool1d.h>
#include <ATen/ops/avg_pool2d.h>
#include <ATen/ops/avg_pool2d_backward.h>
#include <ATen/ops/avg_pool3d.h>
#include <ATen/ops/avg_pool3d_backward.h>
#include <ATen/ops/baddbmm.h>
#include <ATen/ops/bartlett_window.h>
#include <ATen/ops/batch_norm.h>
#include <ATen/ops/batch_norm_backward_elemt.h>
#include <ATen/ops/batch_norm_backward_reduce.h>
#include <ATen/ops/batch_norm_elemt.h>
#include <ATen/ops/batch_norm_gather_stats.h>
#include <ATen/ops/batch_norm_gather_stats_with_counts.h>
#include <ATen/ops/batch_norm_stats.h>
#include <ATen/ops/batch_norm_update_stats.h>
#include <ATen/ops/bernoulli.h>
#include <ATen/ops/bilinear.h>
#include <ATen/ops/binary_cross_entropy.h>
#include <ATen/ops/binary_cross_entropy_backward.h>
#include <ATen/ops/binary_cross_entropy_with_logits.h>
#include <ATen/ops/bincount.h>
#include <ATen/ops/binomial.h>
#include <ATen/ops/bitwise_and.h>
#include <ATen/ops/bitwise_left_shift.h>
#include <ATen/ops/bitwise_not.h>
#include <ATen/ops/bitwise_or.h>
#include <ATen/ops/bitwise_right_shift.h>
#include <ATen/ops/bitwise_xor.h>
#include <ATen/ops/blackman_window.h>
#include <ATen/ops/block_diag.h>
#include <ATen/ops/bmm.h>
#include <ATen/ops/broadcast_tensors.h>
#include <ATen/ops/broadcast_to.h>
#include <ATen/ops/bucketize.h>
#include <ATen/ops/can_cast.h>
#include <ATen/ops/cartesian_prod.h>
#include <ATen/ops/cat.h>
#include <ATen/ops/cauchy.h>
#include <ATen/ops/ccol_indices.h>
#include <ATen/ops/ccol_indices_copy.h>
#include <ATen/ops/cdist.h>
#include <ATen/ops/ceil.h>
#include <ATen/ops/celu.h>
#include <ATen/ops/chain_matmul.h>
#include <ATen/ops/chalf.h>
#include <ATen/ops/channel_shuffle.h>
#include <ATen/ops/cholesky.h>
#include <ATen/ops/cholesky_inverse.h>
#include <ATen/ops/cholesky_solve.h>
#include <ATen/ops/choose_qparams_optimized.h>
#include <ATen/ops/chunk.h>
#include <ATen/ops/clamp.h>
#include <ATen/ops/clamp_max.h>
#include <ATen/ops/clamp_min.h>
#include <ATen/ops/clip.h>
#include <ATen/ops/clone.h>
#include <ATen/ops/coalesce.h>
#include <ATen/ops/col2im.h>
#include <ATen/ops/col_indices.h>
#include <ATen/ops/col_indices_copy.h>
#include <ATen/ops/column_stack.h>
#include <ATen/ops/combinations.h>
#include <ATen/ops/complex.h>
#include <ATen/ops/concat.h>
#include <ATen/ops/concatenate.h>
#include <ATen/ops/conj.h>
#include <ATen/ops/conj_physical.h>
#include <ATen/ops/constant_pad_nd.h>
#include <ATen/ops/contiguous.h>
#include <ATen/ops/conv1d.h>
#include <ATen/ops/conv2d.h>
#include <ATen/ops/conv3d.h>
#include <ATen/ops/conv_depthwise3d.h>
#include <ATen/ops/conv_tbc.h>
#include <ATen/ops/conv_tbc_backward.h>
#include <ATen/ops/conv_transpose1d.h>
#include <ATen/ops/conv_transpose2d.h>
#include <ATen/ops/conv_transpose3d.h>
#include <ATen/ops/convolution.h>
#include <ATen/ops/convolution_backward.h>
#include <ATen/ops/convolution_backward_overrideable.h>
#include <ATen/ops/convolution_overrideable.h>
#include <ATen/ops/copy.h>
#include <ATen/ops/copy_sparse_to_sparse.h>
#include <ATen/ops/copysign.h>
#include <ATen/ops/corrcoef.h>
#include <ATen/ops/cos.h>
#include <ATen/ops/cosh.h>
#include <ATen/ops/cosine_embedding_loss.h>
#include <ATen/ops/cosine_similarity.h>
#include <ATen/ops/count_nonzero.h>
#include <ATen/ops/cov.h>
#include <ATen/ops/cross.h>
#include <ATen/ops/cross_entropy_loss.h>
#include <ATen/ops/crow_indices.h>
#include <ATen/ops/crow_indices_copy.h>
#include <ATen/ops/ctc_loss.h>
#include <ATen/ops/cudnn_affine_grid_generator.h>
#include <ATen/ops/cudnn_affine_grid_generator_backward.h>
#include <ATen/ops/cudnn_batch_norm.h>
#include <ATen/ops/cudnn_batch_norm_backward.h>
#include <ATen/ops/cudnn_convolution.h>
#include <ATen/ops/cudnn_convolution_add_relu.h>
#include <ATen/ops/cudnn_convolution_relu.h>
#include <ATen/ops/cudnn_convolution_transpose.h>
#include <ATen/ops/cudnn_grid_sampler.h>
#include <ATen/ops/cudnn_grid_sampler_backward.h>
#include <ATen/ops/cudnn_is_acceptable.h>
#include <ATen/ops/cummax.h>
#include <ATen/ops/cummaxmin_backward.h>
#include <ATen/ops/cummin.h>
#include <ATen/ops/cumprod.h>
#include <ATen/ops/cumprod_backward.h>
#include <ATen/ops/cumsum.h>
#include <ATen/ops/cumulative_trapezoid.h>
#include <ATen/ops/data.h>
#include <ATen/ops/deg2rad.h>
#include <ATen/ops/dense_dim.h>
#include <ATen/ops/dequantize.h>
#include <ATen/ops/det.h>
#include <ATen/ops/detach.h>
#include <ATen/ops/detach_copy.h>
#include <ATen/ops/diag.h>
#include <ATen/ops/diag_embed.h>
#include <ATen/ops/diagflat.h>
#include <ATen/ops/diagonal.h>
#include <ATen/ops/diagonal_backward.h>
#include <ATen/ops/diagonal_copy.h>
#include <ATen/ops/diagonal_scatter.h>
#include <ATen/ops/diff.h>
#include <ATen/ops/digamma.h>
#include <ATen/ops/dist.h>
#include <ATen/ops/div.h>
#include <ATen/ops/divide.h>
#include <ATen/ops/dot.h>
#include <ATen/ops/dropout.h>
#include <ATen/ops/dsplit.h>
#include <ATen/ops/dstack.h>
#include <ATen/ops/einsum.h>
#include <ATen/ops/elu.h>
#include <ATen/ops/elu_backward.h>
#include <ATen/ops/embedding.h>
#include <ATen/ops/embedding_backward.h>
#include <ATen/ops/embedding_bag.h>
#include <ATen/ops/embedding_dense_backward.h>
#include <ATen/ops/embedding_renorm.h>
#include <ATen/ops/embedding_sparse_backward.h>
#include <ATen/ops/empty.h>
#include <ATen/ops/empty_like.h>
#include <ATen/ops/empty_permuted.h>
#include <ATen/ops/empty_quantized.h>
#include <ATen/ops/empty_strided.h>
#include <ATen/ops/eq.h>
#include <ATen/ops/equal.h>
#include <ATen/ops/erf.h>
#include <ATen/ops/erfc.h>
#include <ATen/ops/erfinv.h>
#include <ATen/ops/exp.h>
#include <ATen/ops/exp2.h>
#include <ATen/ops/expand.h>
#include <ATen/ops/expand_as.h>
#include <ATen/ops/expand_copy.h>
#include <ATen/ops/expm1.h>
#include <ATen/ops/exponential.h>
#include <ATen/ops/eye.h>
#include <ATen/ops/fake_quantize_per_channel_affine.h>
#include <ATen/ops/fake_quantize_per_channel_affine_cachemask.h>
#include <ATen/ops/fake_quantize_per_channel_affine_cachemask_backward.h>
#include <ATen/ops/fake_quantize_per_tensor_affine.h>
#include <ATen/ops/fake_quantize_per_tensor_affine_cachemask.h>
#include <ATen/ops/fake_quantize_per_tensor_affine_cachemask_backward.h>
#include <ATen/ops/fbgemm_linear_fp16_weight.h>
#include <ATen/ops/fbgemm_linear_fp16_weight_fp32_activation.h>
#include <ATen/ops/fbgemm_linear_int8_weight.h>
#include <ATen/ops/fbgemm_linear_int8_weight_fp32_activation.h>
#include <ATen/ops/fbgemm_linear_quantize_weight.h>
#include <ATen/ops/fbgemm_pack_gemm_matrix_fp16.h>
#include <ATen/ops/fbgemm_pack_quantized_matrix.h>
#include <ATen/ops/feature_alpha_dropout.h>
#include <ATen/ops/feature_dropout.h>
#include <ATen/ops/fft_fft.h>
#include <ATen/ops/fft_fft2.h>
#include <ATen/ops/fft_fftfreq.h>
#include <ATen/ops/fft_fftn.h>
#include <ATen/ops/fft_fftshift.h>
#include <ATen/ops/fft_hfft.h>
#include <ATen/ops/fft_hfft2.h>
#include <ATen/ops/fft_hfftn.h>
#include <ATen/ops/fft_ifft.h>
#include <ATen/ops/fft_ifft2.h>
#include <ATen/ops/fft_ifftn.h>
#include <ATen/ops/fft_ifftshift.h>
#include <ATen/ops/fft_ihfft.h>
#include <ATen/ops/fft_ihfft2.h>
#include <ATen/ops/fft_ihfftn.h>
#include <ATen/ops/fft_irfft.h>
#include <ATen/ops/fft_irfft2.h>
#include <ATen/ops/fft_irfftn.h>
#include <ATen/ops/fft_rfft.h>
#include <ATen/ops/fft_rfft2.h>
#include <ATen/ops/fft_rfftfreq.h>
#include <ATen/ops/fft_rfftn.h>
#include <ATen/ops/fill.h>
#include <ATen/ops/fill_diagonal.h>
#include <ATen/ops/fix.h>
#include <ATen/ops/flatten.h>
#include <ATen/ops/flatten_dense_tensors.h>
#include <ATen/ops/flip.h>
#include <ATen/ops/fliplr.h>
#include <ATen/ops/flipud.h>
#include <ATen/ops/float_power.h>
#include <ATen/ops/floor.h>
#include <ATen/ops/floor_divide.h>
#include <ATen/ops/fmax.h>
#include <ATen/ops/fmin.h>
#include <ATen/ops/fmod.h>
#include <ATen/ops/frac.h>
#include <ATen/ops/fractional_max_pool2d.h>
#include <ATen/ops/fractional_max_pool2d_backward.h>
#include <ATen/ops/fractional_max_pool3d.h>
#include <ATen/ops/fractional_max_pool3d_backward.h>
#include <ATen/ops/frexp.h>
#include <ATen/ops/frobenius_norm.h>
#include <ATen/ops/from_file.h>
#include <ATen/ops/full.h>
#include <ATen/ops/full_like.h>
#include <ATen/ops/fused_moving_avg_obs_fake_quant.h>
#include <ATen/ops/gather.h>
#include <ATen/ops/gather_backward.h>
#include <ATen/ops/gcd.h>
#include <ATen/ops/ge.h>
#include <ATen/ops/gelu.h>
#include <ATen/ops/gelu_backward.h>
#include <ATen/ops/geometric.h>
#include <ATen/ops/geqrf.h>
#include <ATen/ops/ger.h>
#include <ATen/ops/glu.h>
#include <ATen/ops/glu_backward.h>
#include <ATen/ops/glu_backward_jvp.h>
#include <ATen/ops/glu_jvp.h>
#include <ATen/ops/gradient.h>
#include <ATen/ops/greater.h>
#include <ATen/ops/greater_equal.h>
#include <ATen/ops/grid_sampler.h>
#include <ATen/ops/grid_sampler_2d.h>
#include <ATen/ops/grid_sampler_2d_backward.h>
#include <ATen/ops/grid_sampler_3d.h>
#include <ATen/ops/grid_sampler_3d_backward.h>
#include <ATen/ops/group_norm.h>
#include <ATen/ops/gru.h>
#include <ATen/ops/gru_cell.h>
#include <ATen/ops/gt.h>
#include <ATen/ops/hamming_window.h>
#include <ATen/ops/hann_window.h>
#include <ATen/ops/hardshrink.h>
#include <ATen/ops/hardshrink_backward.h>
#include <ATen/ops/hardsigmoid.h>
#include <ATen/ops/hardsigmoid_backward.h>
#include <ATen/ops/hardswish.h>
#include <ATen/ops/hardswish_backward.h>
#include <ATen/ops/hardtanh.h>
#include <ATen/ops/hardtanh_backward.h>
#include <ATen/ops/heaviside.h>
#include <ATen/ops/hinge_embedding_loss.h>
#include <ATen/ops/histc.h>
#include <ATen/ops/histogram.h>
#include <ATen/ops/histogramdd.h>
#include <ATen/ops/hsplit.h>
#include <ATen/ops/hspmm.h>
#include <ATen/ops/hstack.h>
#include <ATen/ops/huber_loss.h>
#include <ATen/ops/huber_loss_backward.h>
#include <ATen/ops/hypot.h>
#include <ATen/ops/i0.h>
#include <ATen/ops/igamma.h>
#include <ATen/ops/igammac.h>
#include <ATen/ops/im2col.h>
#include <ATen/ops/imag.h>
#include <ATen/ops/index.h>
#include <ATen/ops/index_add.h>
#include <ATen/ops/index_copy.h>
#include <ATen/ops/index_fill.h>
#include <ATen/ops/index_put.h>
#include <ATen/ops/index_reduce.h>
#include <ATen/ops/index_select.h>
#include <ATen/ops/index_select_backward.h>
#include <ATen/ops/indices.h>
#include <ATen/ops/indices_copy.h>
#include <ATen/ops/infinitely_differentiable_gelu_backward.h>
#include <ATen/ops/inner.h>
#include <ATen/ops/instance_norm.h>
#include <ATen/ops/int_repr.h>
#include <ATen/ops/inverse.h>
#include <ATen/ops/is_coalesced.h>
#include <ATen/ops/is_complex.h>
#include <ATen/ops/is_conj.h>
#include <ATen/ops/is_distributed.h>
#include <ATen/ops/is_floating_point.h>
#include <ATen/ops/is_inference.h>
#include <ATen/ops/is_leaf.h>
#include <ATen/ops/is_neg.h>
#include <ATen/ops/is_nonzero.h>
#include <ATen/ops/is_pinned.h>
#include <ATen/ops/is_same_size.h>
#include <ATen/ops/is_set_to.h>
#include <ATen/ops/is_signed.h>
#include <ATen/ops/is_vulkan_available.h>
#include <ATen/ops/isclose.h>
#include <ATen/ops/isfinite.h>
#include <ATen/ops/isin.h>
#include <ATen/ops/isinf.h>
#include <ATen/ops/isnan.h>
#include <ATen/ops/isneginf.h>
#include <ATen/ops/isposinf.h>
#include <ATen/ops/isreal.h>
#include <ATen/ops/istft.h>
#include <ATen/ops/item.h>
#include <ATen/ops/kaiser_window.h>
#include <ATen/ops/kl_div.h>
#include <ATen/ops/kron.h>
#include <ATen/ops/kthvalue.h>
#include <ATen/ops/l1_loss.h>
#include <ATen/ops/layer_norm.h>
#include <ATen/ops/lcm.h>
#include <ATen/ops/ldexp.h>
#include <ATen/ops/le.h>
#include <ATen/ops/leaky_relu.h>
#include <ATen/ops/leaky_relu_backward.h>
#include <ATen/ops/lerp.h>
#include <ATen/ops/less.h>
#include <ATen/ops/less_equal.h>
#include <ATen/ops/lgamma.h>
#include <ATen/ops/lift.h>
#include <ATen/ops/lift_fresh.h>
#include <ATen/ops/lift_fresh_copy.h>
#include <ATen/ops/linalg_cholesky.h>
#include <ATen/ops/linalg_cholesky_ex.h>
#include <ATen/ops/linalg_cond.h>
#include <ATen/ops/linalg_cross.h>
#include <ATen/ops/linalg_det.h>
#include <ATen/ops/linalg_diagonal.h>
#include <ATen/ops/linalg_eig.h>
#include <ATen/ops/linalg_eigh.h>
#include <ATen/ops/linalg_eigvals.h>
#include <ATen/ops/linalg_eigvalsh.h>
#include <ATen/ops/linalg_householder_product.h>
#include <ATen/ops/linalg_inv.h>
#include <ATen/ops/linalg_inv_ex.h>
#include <ATen/ops/linalg_ldl_factor.h>
#include <ATen/ops/linalg_ldl_factor_ex.h>
#include <ATen/ops/linalg_ldl_solve.h>
#include <ATen/ops/linalg_lstsq.h>
#include <ATen/ops/linalg_lu.h>
#include <ATen/ops/linalg_lu_factor.h>
#include <ATen/ops/linalg_lu_factor_ex.h>
#include <ATen/ops/linalg_lu_solve.h>
#include <ATen/ops/linalg_matmul.h>
#include <ATen/ops/linalg_matrix_exp.h>
#include <ATen/ops/linalg_matrix_norm.h>
#include <ATen/ops/linalg_matrix_power.h>
#include <ATen/ops/linalg_matrix_rank.h>
#include <ATen/ops/linalg_multi_dot.h>
#include <ATen/ops/linalg_norm.h>
#include <ATen/ops/linalg_pinv.h>
#include <ATen/ops/linalg_qr.h>
#include <ATen/ops/linalg_slogdet.h>
#include <ATen/ops/linalg_solve.h>
#include <ATen/ops/linalg_solve_ex.h>
#include <ATen/ops/linalg_solve_triangular.h>
#include <ATen/ops/linalg_svd.h>
#include <ATen/ops/linalg_svdvals.h>
#include <ATen/ops/linalg_tensorinv.h>
#include <ATen/ops/linalg_tensorsolve.h>
#include <ATen/ops/linalg_vander.h>
#include <ATen/ops/linalg_vecdot.h>
#include <ATen/ops/linalg_vector_norm.h>
#include <ATen/ops/linear.h>
#include <ATen/ops/linear_backward.h>
#include <ATen/ops/linspace.h>
#include <ATen/ops/log.h>
#include <ATen/ops/log10.h>
#include <ATen/ops/log1p.h>
#include <ATen/ops/log2.h>
#include <ATen/ops/log_normal.h>
#include <ATen/ops/log_sigmoid.h>
#include <ATen/ops/log_sigmoid_backward.h>
#include <ATen/ops/log_sigmoid_forward.h>
#include <ATen/ops/log_softmax.h>
#include <ATen/ops/logaddexp.h>
#include <ATen/ops/logaddexp2.h>
#include <ATen/ops/logcumsumexp.h>
#include <ATen/ops/logdet.h>
#include <ATen/ops/logical_and.h>
#include <ATen/ops/logical_not.h>
#include <ATen/ops/logical_or.h>
#include <ATen/ops/logical_xor.h>
#include <ATen/ops/logit.h>
#include <ATen/ops/logit_backward.h>
#include <ATen/ops/logspace.h>
#include <ATen/ops/logsumexp.h>
#include <ATen/ops/lshift.h>
#include <ATen/ops/lstm.h>
#include <ATen/ops/lstm_cell.h>
#include <ATen/ops/lstm_mps_backward.h>
#include <ATen/ops/lt.h>
#include <ATen/ops/lu_solve.h>
#include <ATen/ops/lu_unpack.h>
#include <ATen/ops/mH.h>
#include <ATen/ops/mT.h>
#include <ATen/ops/margin_ranking_loss.h>
#include <ATen/ops/masked_fill.h>
#include <ATen/ops/masked_scatter.h>
#include <ATen/ops/masked_scatter_backward.h>
#include <ATen/ops/masked_select.h>
#include <ATen/ops/masked_select_backward.h>
#include <ATen/ops/matmul.h>
#include <ATen/ops/matmul_backward.h>
#include <ATen/ops/matrix_H.h>
#include <ATen/ops/matrix_exp.h>
#include <ATen/ops/matrix_exp_backward.h>
#include <ATen/ops/matrix_power.h>
#include <ATen/ops/max.h>
#include <ATen/ops/max_pool1d.h>
#include <ATen/ops/max_pool1d_with_indices.h>
#include <ATen/ops/max_pool2d.h>
#include <ATen/ops/max_pool2d_backward.h>
#include <ATen/ops/max_pool2d_with_indices.h>
#include <ATen/ops/max_pool2d_with_indices_backward.h>
#include <ATen/ops/max_pool3d.h>
#include <ATen/ops/max_pool3d_with_indices.h>
#include <ATen/ops/max_pool3d_with_indices_backward.h>
#include <ATen/ops/max_unpool2d.h>
#include <ATen/ops/max_unpool3d.h>
#include <ATen/ops/maximum.h>
#include <ATen/ops/mean.h>
#include <ATen/ops/median.h>
#include <ATen/ops/meshgrid.h>
#include <ATen/ops/min.h>
#include <ATen/ops/minimum.h>
#include <ATen/ops/miopen_batch_norm.h>
#include <ATen/ops/miopen_batch_norm_backward.h>
#include <ATen/ops/miopen_convolution.h>
#include <ATen/ops/miopen_convolution_add_relu.h>
#include <ATen/ops/miopen_convolution_relu.h>
#include <ATen/ops/miopen_convolution_transpose.h>
#include <ATen/ops/miopen_depthwise_convolution.h>
#include <ATen/ops/miopen_rnn.h>
#include <ATen/ops/miopen_rnn_backward.h>
#include <ATen/ops/mish.h>
#include <ATen/ops/mish_backward.h>
#include <ATen/ops/mkldnn_adaptive_avg_pool2d.h>
#include <ATen/ops/mkldnn_adaptive_avg_pool2d_backward.h>
#include <ATen/ops/mkldnn_convolution.h>
#include <ATen/ops/mkldnn_linear.h>
#include <ATen/ops/mkldnn_linear_backward.h>
#include <ATen/ops/mkldnn_linear_backward_input.h>
#include <ATen/ops/mkldnn_linear_backward_weights.h>
#include <ATen/ops/mkldnn_max_pool2d.h>
#include <ATen/ops/mkldnn_max_pool2d_backward.h>
#include <ATen/ops/mkldnn_max_pool3d.h>
#include <ATen/ops/mkldnn_max_pool3d_backward.h>
#include <ATen/ops/mkldnn_reorder_conv2d_weight.h>
#include <ATen/ops/mkldnn_reorder_conv3d_weight.h>
#include <ATen/ops/mkldnn_rnn_layer.h>
#include <ATen/ops/mkldnn_rnn_layer_backward.h>
#include <ATen/ops/mm.h>
#include <ATen/ops/mode.h>
#include <ATen/ops/moveaxis.h>
#include <ATen/ops/movedim.h>
#include <ATen/ops/mps_convolution_backward.h>
#include <ATen/ops/mps_convolution_transpose_backward.h>
#include <ATen/ops/mse_loss.h>
#include <ATen/ops/mse_loss_backward.h>
#include <ATen/ops/msort.h>
#include <ATen/ops/mul.h>
#include <ATen/ops/multi_margin_loss.h>
#include <ATen/ops/multi_margin_loss_backward.h>
#include <ATen/ops/multilabel_margin_loss.h>
#include <ATen/ops/multilabel_margin_loss_backward.h>
#include <ATen/ops/multilabel_margin_loss_forward.h>
#include <ATen/ops/multinomial.h>
#include <ATen/ops/multiply.h>
#include <ATen/ops/mv.h>
#include <ATen/ops/mvlgamma.h>
#include <ATen/ops/nan_to_num.h>
#include <ATen/ops/nanmean.h>
#include <ATen/ops/nanmedian.h>
#include <ATen/ops/nanquantile.h>
#include <ATen/ops/nansum.h>
#include <ATen/ops/narrow.h>
#include <ATen/ops/narrow_copy.h>
#include <ATen/ops/native_batch_norm.h>
#include <ATen/ops/native_batch_norm_backward.h>
#include <ATen/ops/native_channel_shuffle.h>
#include <ATen/ops/native_dropout.h>
#include <ATen/ops/native_dropout_backward.h>
#include <ATen/ops/native_group_norm.h>
#include <ATen/ops/native_group_norm_backward.h>
#include <ATen/ops/native_layer_norm.h>
#include <ATen/ops/native_layer_norm_backward.h>
#include <ATen/ops/native_norm.h>
#include <ATen/ops/ne.h>
#include <ATen/ops/neg.h>
#include <ATen/ops/negative.h>
#include <ATen/ops/nested_to_padded_tensor.h>
#include <ATen/ops/new_empty.h>
#include <ATen/ops/new_empty_strided.h>
#include <ATen/ops/new_full.h>
#include <ATen/ops/new_ones.h>
#include <ATen/ops/new_zeros.h>
#include <ATen/ops/nextafter.h>
#include <ATen/ops/nll_loss.h>
#include <ATen/ops/nll_loss2d.h>
#include <ATen/ops/nll_loss2d_backward.h>
#include <ATen/ops/nll_loss2d_forward.h>
#include <ATen/ops/nll_loss_backward.h>
#include <ATen/ops/nll_loss_forward.h>
#include <ATen/ops/nll_loss_nd.h>
#include <ATen/ops/nonzero.h>
#include <ATen/ops/nonzero_numpy.h>
#include <ATen/ops/nonzero_static.h>
#include <ATen/ops/norm.h>
#include <ATen/ops/norm_except_dim.h>
#include <ATen/ops/normal.h>
#include <ATen/ops/not_equal.h>
#include <ATen/ops/nuclear_norm.h>
#include <ATen/ops/numpy_T.h>
#include <ATen/ops/one_hot.h>
#include <ATen/ops/ones.h>
#include <ATen/ops/ones_like.h>
#include <ATen/ops/or.h>
#include <ATen/ops/orgqr.h>
#include <ATen/ops/ormqr.h>
#include <ATen/ops/outer.h>
#include <ATen/ops/output_nr.h>
#include <ATen/ops/pad.h>
#include <ATen/ops/pad_sequence.h>
#include <ATen/ops/pairwise_distance.h>
#include <ATen/ops/pdist.h>
#include <ATen/ops/permute.h>
#include <ATen/ops/permute_copy.h>
#include <ATen/ops/pin_memory.h>
#include <ATen/ops/pinverse.h>
#include <ATen/ops/pixel_shuffle.h>
#include <ATen/ops/pixel_unshuffle.h>
#include <ATen/ops/poisson.h>
#include <ATen/ops/poisson_nll_loss.h>
#include <ATen/ops/polar.h>
#include <ATen/ops/polygamma.h>
#include <ATen/ops/positive.h>
#include <ATen/ops/pow.h>
#include <ATen/ops/prelu.h>
#include <ATen/ops/prod.h>
#include <ATen/ops/promote_types.h>
#include <ATen/ops/put.h>
#include <ATen/ops/q_per_channel_axis.h>
#include <ATen/ops/q_per_channel_scales.h>
#include <ATen/ops/q_per_channel_zero_points.h>
#include <ATen/ops/q_scale.h>
#include <ATen/ops/q_zero_point.h>
#include <ATen/ops/qr.h>
#include <ATen/ops/qscheme.h>
#include <ATen/ops/quantile.h>
#include <ATen/ops/quantize_per_channel.h>
#include <ATen/ops/quantize_per_tensor.h>
#include <ATen/ops/quantize_per_tensor_dynamic.h>
#include <ATen/ops/quantized_batch_norm.h>
#include <ATen/ops/quantized_gru_cell.h>
#include <ATen/ops/quantized_lstm_cell.h>
#include <ATen/ops/quantized_max_pool1d.h>
#include <ATen/ops/quantized_max_pool2d.h>
#include <ATen/ops/quantized_max_pool3d.h>
#include <ATen/ops/quantized_rnn_relu_cell.h>
#include <ATen/ops/quantized_rnn_tanh_cell.h>
#include <ATen/ops/rad2deg.h>
#include <ATen/ops/rand.h>
#include <ATen/ops/rand_like.h>
#include <ATen/ops/randint.h>
#include <ATen/ops/randint_like.h>
#include <ATen/ops/randn.h>
#include <ATen/ops/randn_like.h>
#include <ATen/ops/random.h>
#include <ATen/ops/randperm.h>
#include <ATen/ops/range.h>
#include <ATen/ops/ravel.h>
#include <ATen/ops/real.h>
#include <ATen/ops/reciprocal.h>
#include <ATen/ops/record_stream.h>
#include <ATen/ops/refine_names.h>
#include <ATen/ops/reflection_pad1d.h>
#include <ATen/ops/reflection_pad1d_backward.h>
#include <ATen/ops/reflection_pad2d.h>
#include <ATen/ops/reflection_pad2d_backward.h>
#include <ATen/ops/reflection_pad3d.h>
#include <ATen/ops/reflection_pad3d_backward.h>
#include <ATen/ops/relu.h>
#include <ATen/ops/relu6.h>
#include <ATen/ops/remainder.h>
#include <ATen/ops/rename.h>
#include <ATen/ops/renorm.h>
#include <ATen/ops/repeat.h>
#include <ATen/ops/repeat_interleave.h>
#include <ATen/ops/replication_pad1d.h>
#include <ATen/ops/replication_pad1d_backward.h>
#include <ATen/ops/replication_pad2d.h>
#include <ATen/ops/replication_pad2d_backward.h>
#include <ATen/ops/replication_pad3d.h>
#include <ATen/ops/replication_pad3d_backward.h>
#include <ATen/ops/requires_grad.h>
#include <ATen/ops/reshape.h>
#include <ATen/ops/reshape_as.h>
#include <ATen/ops/resize.h>
#include <ATen/ops/resize_as.h>
#include <ATen/ops/resize_as_sparse.h>
#include <ATen/ops/resolve_conj.h>
#include <ATen/ops/resolve_neg.h>
#include <ATen/ops/result_type.h>
#include <ATen/ops/retain_grad.h>
#include <ATen/ops/retains_grad.h>
#include <ATen/ops/rnn_relu.h>
#include <ATen/ops/rnn_relu_cell.h>
#include <ATen/ops/rnn_tanh.h>
#include <ATen/ops/rnn_tanh_cell.h>
#include <ATen/ops/roll.h>
#include <ATen/ops/rot90.h>
#include <ATen/ops/round.h>
#include <ATen/ops/row_indices.h>
#include <ATen/ops/row_indices_copy.h>
#include <ATen/ops/row_stack.h>
#include <ATen/ops/rrelu.h>
#include <ATen/ops/rrelu_with_noise.h>
#include <ATen/ops/rrelu_with_noise_backward.h>
#include <ATen/ops/rshift.h>
#include <ATen/ops/rsqrt.h>
#include <ATen/ops/rsub.h>
#include <ATen/ops/scalar_tensor.h>
#include <ATen/ops/scaled_dot_product_attention.h>
#include <ATen/ops/scatter.h>
#include <ATen/ops/scatter_add.h>
#include <ATen/ops/scatter_reduce.h>
#include <ATen/ops/searchsorted.h>
#include <ATen/ops/segment_reduce.h>
#include <ATen/ops/select.h>
#include <ATen/ops/select_backward.h>
#include <ATen/ops/select_copy.h>
#include <ATen/ops/select_scatter.h>
#include <ATen/ops/selu.h>
#include <ATen/ops/set.h>
#include <ATen/ops/set_data.h>
#include <ATen/ops/sgn.h>
#include <ATen/ops/sigmoid.h>
#include <ATen/ops/sigmoid_backward.h>
#include <ATen/ops/sign.h>
#include <ATen/ops/signbit.h>
#include <ATen/ops/silu.h>
#include <ATen/ops/silu_backward.h>
#include <ATen/ops/sin.h>
#include <ATen/ops/sinc.h>
#include <ATen/ops/sinh.h>
#include <ATen/ops/size.h>
#include <ATen/ops/slice.h>
#include <ATen/ops/slice_backward.h>
#include <ATen/ops/slice_copy.h>
#include <ATen/ops/slice_scatter.h>
#include <ATen/ops/slogdet.h>
#include <ATen/ops/slow_conv3d.h>
#include <ATen/ops/slow_conv3d_forward.h>
#include <ATen/ops/slow_conv_dilated2d.h>
#include <ATen/ops/slow_conv_dilated3d.h>
#include <ATen/ops/slow_conv_transpose2d.h>
#include <ATen/ops/slow_conv_transpose3d.h>
#include <ATen/ops/smm.h>
#include <ATen/ops/smooth_l1_loss.h>
#include <ATen/ops/smooth_l1_loss_backward.h>
#include <ATen/ops/soft_margin_loss.h>
#include <ATen/ops/soft_margin_loss_backward.h>
#include <ATen/ops/softmax.h>
#include <ATen/ops/softplus.h>
#include <ATen/ops/softplus_backward.h>
#include <ATen/ops/softshrink.h>
#include <ATen/ops/softshrink_backward.h>
#include <ATen/ops/sort.h>
#include <ATen/ops/sparse_bsc_tensor.h>
#include <ATen/ops/sparse_bsr_tensor.h>
#include <ATen/ops/sparse_compressed_tensor.h>
#include <ATen/ops/sparse_coo_tensor.h>
#include <ATen/ops/sparse_csc_tensor.h>
#include <ATen/ops/sparse_csr_tensor.h>
#include <ATen/ops/sparse_dim.h>
#include <ATen/ops/sparse_mask.h>
#include <ATen/ops/sparse_resize.h>
#include <ATen/ops/sparse_resize_and_clear.h>
#include <ATen/ops/sparse_sampled_addmm.h>
#include <ATen/ops/special_airy_ai.h>
#include <ATen/ops/special_bessel_j0.h>
#include <ATen/ops/special_bessel_j1.h>
#include <ATen/ops/special_bessel_y0.h>
#include <ATen/ops/special_bessel_y1.h>
#include <ATen/ops/special_chebyshev_polynomial_t.h>
#include <ATen/ops/special_chebyshev_polynomial_u.h>
#include <ATen/ops/special_chebyshev_polynomial_v.h>
#include <ATen/ops/special_chebyshev_polynomial_w.h>
#include <ATen/ops/special_digamma.h>
#include <ATen/ops/special_entr.h>
#include <ATen/ops/special_erf.h>
#include <ATen/ops/special_erfc.h>
#include <ATen/ops/special_erfcx.h>
#include <ATen/ops/special_erfinv.h>
#include <ATen/ops/special_exp2.h>
#include <ATen/ops/special_expit.h>
#include <ATen/ops/special_expm1.h>
#include <ATen/ops/special_gammainc.h>
#include <ATen/ops/special_gammaincc.h>
#include <ATen/ops/special_gammaln.h>
#include <ATen/ops/special_hermite_polynomial_h.h>
#include <ATen/ops/special_hermite_polynomial_he.h>
#include <ATen/ops/special_i0.h>
#include <ATen/ops/special_i0e.h>
#include <ATen/ops/special_i1.h>
#include <ATen/ops/special_i1e.h>
#include <ATen/ops/special_laguerre_polynomial_l.h>
#include <ATen/ops/special_legendre_polynomial_p.h>
#include <ATen/ops/special_log1p.h>
#include <ATen/ops/special_log_ndtr.h>
#include <ATen/ops/special_log_softmax.h>
#include <ATen/ops/special_logit.h>
#include <ATen/ops/special_logsumexp.h>
#include <ATen/ops/special_modified_bessel_i0.h>
#include <ATen/ops/special_modified_bessel_i1.h>
#include <ATen/ops/special_modified_bessel_k0.h>
#include <ATen/ops/special_modified_bessel_k1.h>
#include <ATen/ops/special_multigammaln.h>
#include <ATen/ops/special_ndtr.h>
#include <ATen/ops/special_ndtri.h>
#include <ATen/ops/special_polygamma.h>
#include <ATen/ops/special_psi.h>
#include <ATen/ops/special_round.h>
#include <ATen/ops/special_scaled_modified_bessel_k0.h>
#include <ATen/ops/special_scaled_modified_bessel_k1.h>
#include <ATen/ops/special_shifted_chebyshev_polynomial_t.h>
#include <ATen/ops/special_shifted_chebyshev_polynomial_u.h>
#include <ATen/ops/special_shifted_chebyshev_polynomial_v.h>
#include <ATen/ops/special_shifted_chebyshev_polynomial_w.h>
#include <ATen/ops/special_sinc.h>
#include <ATen/ops/special_softmax.h>
#include <ATen/ops/special_spherical_bessel_j0.h>
#include <ATen/ops/special_xlog1py.h>
#include <ATen/ops/special_xlogy.h>
#include <ATen/ops/special_zeta.h>
#include <ATen/ops/split.h>
#include <ATen/ops/split_copy.h>
#include <ATen/ops/split_with_sizes.h>
#include <ATen/ops/split_with_sizes_copy.h>
#include <ATen/ops/sqrt.h>
#include <ATen/ops/square.h>
#include <ATen/ops/squeeze.h>
#include <ATen/ops/squeeze_copy.h>
#include <ATen/ops/sspaddmm.h>
#include <ATen/ops/stack.h>
#include <ATen/ops/std.h>
#include <ATen/ops/std_mean.h>
#include <ATen/ops/stft.h>
#include <ATen/ops/stride.h>
#include <ATen/ops/sub.h>
#include <ATen/ops/subtract.h>
#include <ATen/ops/sum.h>
#include <ATen/ops/sum_to_size.h>
#include <ATen/ops/svd.h>
#include <ATen/ops/swapaxes.h>
#include <ATen/ops/swapdims.h>
#include <ATen/ops/sym_constrain_range.h>
#include <ATen/ops/sym_constrain_range_for_size.h>
#include <ATen/ops/sym_numel.h>
#include <ATen/ops/sym_size.h>
#include <ATen/ops/sym_storage_offset.h>
#include <ATen/ops/sym_stride.h>
#include <ATen/ops/t.h>
#include <ATen/ops/t_copy.h>
#include <ATen/ops/take.h>
#include <ATen/ops/take_along_dim.h>
#include <ATen/ops/tan.h>
#include <ATen/ops/tanh.h>
#include <ATen/ops/tanh_backward.h>
#include <ATen/ops/tensor_split.h>
#include <ATen/ops/tensordot.h>
#include <ATen/ops/thnn_conv2d.h>
#include <ATen/ops/threshold.h>
#include <ATen/ops/threshold_backward.h>
#include <ATen/ops/tile.h>
#include <ATen/ops/to.h>
#include <ATen/ops/to_dense.h>
#include <ATen/ops/to_dense_backward.h>
#include <ATen/ops/to_mkldnn.h>
#include <ATen/ops/to_mkldnn_backward.h>
#include <ATen/ops/to_padded_tensor.h>
#include <ATen/ops/to_sparse.h>
#include <ATen/ops/to_sparse_bsc.h>
#include <ATen/ops/to_sparse_bsr.h>
#include <ATen/ops/to_sparse_csc.h>
#include <ATen/ops/to_sparse_csr.h>
#include <ATen/ops/topk.h>
#include <ATen/ops/trace.h>
#include <ATen/ops/trace_backward.h>
#include <ATen/ops/transpose.h>
#include <ATen/ops/transpose_copy.h>
#include <ATen/ops/trapezoid.h>
#include <ATen/ops/trapz.h>
#include <ATen/ops/triangular_solve.h>
#include <ATen/ops/tril.h>
#include <ATen/ops/tril_indices.h>
#include <ATen/ops/triplet_margin_loss.h>
#include <ATen/ops/triu.h>
#include <ATen/ops/triu_indices.h>
#include <ATen/ops/true_divide.h>
#include <ATen/ops/trunc.h>
#include <ATen/ops/type_as.h>
#include <ATen/ops/unbind.h>
#include <ATen/ops/unbind_copy.h>
#include <ATen/ops/unflatten.h>
#include <ATen/ops/unflatten_dense_tensors.h>
#include <ATen/ops/unfold.h>
#include <ATen/ops/unfold_backward.h>
#include <ATen/ops/unfold_copy.h>
#include <ATen/ops/uniform.h>
#include <ATen/ops/unique_consecutive.h>
#include <ATen/ops/unique_dim.h>
#include <ATen/ops/unique_dim_consecutive.h>
#include <ATen/ops/unsafe_chunk.h>
#include <ATen/ops/unsafe_split.h>
#include <ATen/ops/unsafe_split_with_sizes.h>
#include <ATen/ops/unsqueeze.h>
#include <ATen/ops/unsqueeze_copy.h>
#include <ATen/ops/upsample_bicubic2d.h>
#include <ATen/ops/upsample_bicubic2d_backward.h>
#include <ATen/ops/upsample_bilinear2d.h>
#include <ATen/ops/upsample_bilinear2d_backward.h>
#include <ATen/ops/upsample_linear1d.h>
#include <ATen/ops/upsample_linear1d_backward.h>
#include <ATen/ops/upsample_nearest1d.h>
#include <ATen/ops/upsample_nearest1d_backward.h>
#include <ATen/ops/upsample_nearest2d.h>
#include <ATen/ops/upsample_nearest2d_backward.h>
#include <ATen/ops/upsample_nearest3d.h>
#include <ATen/ops/upsample_nearest3d_backward.h>
#include <ATen/ops/upsample_trilinear3d.h>
#include <ATen/ops/upsample_trilinear3d_backward.h>
#include <ATen/ops/value_selecting_reduction_backward.h>
#include <ATen/ops/values.h>
#include <ATen/ops/values_copy.h>
#include <ATen/ops/vander.h>
#include <ATen/ops/var.h>
#include <ATen/ops/var_mean.h>
#include <ATen/ops/vdot.h>
#include <ATen/ops/view.h>
#include <ATen/ops/view_as.h>
#include <ATen/ops/view_as_complex.h>
#include <ATen/ops/view_as_complex_copy.h>
#include <ATen/ops/view_as_real.h>
#include <ATen/ops/view_as_real_copy.h>
#include <ATen/ops/view_copy.h>
#include <ATen/ops/vsplit.h>
#include <ATen/ops/vstack.h>
#include <ATen/ops/where.h>
#include <ATen/ops/xlogy.h>
#include <ATen/ops/xor.h>
#include <ATen/ops/zero.h>
#include <ATen/ops/zeros.h>
#include <ATen/ops/zeros_like.h>

namespace at {



// Special C++ only overloads for std()-like functions (See gh-40287)
// These are needed because int -> bool conversion takes precedence over int -> IntArrayRef
// So, for example std(0) would select the std(unbiased=False) overload
TORCH_API inline Tensor var(const Tensor& self, int dim) {
  return at::var(self, IntArrayRef{dim});
}
TORCH_API inline std::tuple<Tensor, Tensor> var_mean(const Tensor& self, int dim) {
  return at::var_mean(self, IntArrayRef{dim});
}
TORCH_API inline Tensor std(const Tensor& self, int dim) {
  return at::std(self, IntArrayRef{dim});
}
TORCH_API inline std::tuple<Tensor, Tensor> std_mean(const Tensor& self, int dim) {
  return at::std_mean(self, IntArrayRef{dim});
}

inline int64_t numel(const Tensor& tensor) {
  return tensor.numel();
}

inline int64_t size(const Tensor& tensor, int64_t dim) {
  return tensor.size(dim);
}

inline int64_t stride(const Tensor& tensor, int64_t dim) {
  return tensor.stride(dim);
}

inline bool is_complex(const Tensor& tensor) {
  return tensor.is_complex();
}

inline bool is_floating_point(const Tensor& tensor) {
  return tensor.is_floating_point();
}

inline bool is_signed(const Tensor& tensor) {
  return tensor.is_signed();
}

inline bool is_inference(const Tensor& tensor) {
  return tensor.is_inference();
}

inline bool _is_zerotensor(const Tensor& tensor) {
  return tensor._is_zerotensor();
}

inline bool is_conj(const Tensor& tensor) {
  return tensor.is_conj();
}

inline Tensor conj(const Tensor& tensor) {
  return tensor.conj();
}

inline bool is_neg(const Tensor& tensor) {
  return tensor.is_neg();
}

}
