#!/usr/bin/env python3
"""
构建NSIS安装包的完整脚本
"""
import os
import sys
import shutil
import subprocess
from pathlib import Path
import requests
import zipfile

class NSISBuilder:
    def __init__(self):
        self.build_dir = Path("build_installer")
        self.nsis_dir = Path("C:/Program Files (x86)/NSIS")  # 默认NSIS安装路径
        
    def check_nsis(self):
        """检查NSIS是否已安装"""
        makensis = self.nsis_dir / "makensis.exe"
        if not makensis.exists():
            print("错误: 未找到NSIS安装")
            print("请从 https://nsis.sourceforge.io/Download 下载并安装NSIS")
            return False
        return True
        
    def download_vcredist(self):
        """下载Visual C++ Redistributable"""
        print("下载Visual C++ Redistributable...")
        
        vcredist_url = "https://aka.ms/vs/17/release/vc_redist.x64.exe"
        vcredist_file = self.build_dir / "vcredist_x64.exe"
        
        if not vcredist_file.exists():
            try:
                response = requests.get(vcredist_url, stream=True)
                with open(vcredist_file, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                print("Visual C++ Redistributable下载完成")
            except Exception as e:
                print(f"下载Visual C++ Redistributable失败: {e}")
                print("请手动下载并放置在build_installer目录中")
                
    def create_license(self):
        """创建许可证文件"""
        license_content = """LocalQA 公司制度查询平台
版权所有 (C) 2024 您的公司名称

本软件按"原样"提供，不提供任何明示或暗示的保证。
使用本软件的风险由用户自行承担。

本软件仅供内部使用，未经授权不得分发或修改。
"""
        license_file = self.build_dir / "LICENSE.txt"
        license_file.write_text(license_content, encoding='utf-8')
        
    def prepare_build_environment(self):
        """准备构建环境"""
        print("准备构建环境...")
        
        # 创建构建目录
        self.build_dir.mkdir(exist_ok=True)
        
        # 检查便携式环境是否存在
        portable_dir = Path("build_portable")
        if not portable_dir.exists():
            print("错误: 便携式环境不存在")
            print("请先运行 prepare_portable_env.py 创建便携式环境")
            return False
            
        # 复制便携式环境到构建目录
        build_portable = self.build_dir / "build_portable"
        if build_portable.exists():
            shutil.rmtree(build_portable)
        shutil.copytree(portable_dir, build_portable)
        
        # 复制NSIS脚本
        nsi_src = Path("build_scripts/LocalQA_installer.nsi")
        nsi_dst = self.build_dir / "LocalQA_installer.nsi"
        shutil.copy2(nsi_src, nsi_dst)
        
        # 下载依赖文件
        self.download_vcredist()
        self.create_license()
        
        return True
        
    def build_installer(self):
        """构建安装程序"""
        print("构建NSIS安装程序...")
        
        makensis = self.nsis_dir / "makensis.exe"
        nsi_file = self.build_dir / "LocalQA_installer.nsi"
        
        # 切换到构建目录
        original_cwd = os.getcwd()
        os.chdir(self.build_dir)
        
        try:
            # 运行NSIS编译器
            cmd = [str(makensis), str(nsi_file.name)]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("安装程序构建成功!")
                print(f"输出文件: {self.build_dir / 'LocalQA_Setup_1.0.0.exe'}")
            else:
                print("构建失败:")
                print(result.stdout)
                print(result.stderr)
                
        finally:
            os.chdir(original_cwd)
            
    def optimize_installer(self):
        """优化安装程序"""
        print("优化安装程序...")
        
        # 可以在这里添加额外的优化步骤
        # 比如压缩、签名等
        
        installer_file = self.build_dir / "LocalQA_Setup_1.0.0.exe"
        if installer_file.exists():
            size_mb = installer_file.stat().st_size / (1024 * 1024)
            print(f"安装程序大小: {size_mb:.1f} MB")
            
            # 移动到项目根目录
            final_installer = Path("LocalQA_Setup_1.0.0.exe")
            if final_installer.exists():
                final_installer.unlink()
            shutil.move(installer_file, final_installer)
            print(f"最终安装程序: {final_installer}")
            
    def build(self):
        """完整构建流程"""
        print("开始构建NSIS安装程序...")
        
        if not self.check_nsis():
            return False
            
        if not self.prepare_build_environment():
            return False
            
        self.build_installer()
        self.optimize_installer()
        
        print("构建完成!")
        return True

def main():
    """主函数"""
    print("LocalQA NSIS安装程序构建器")
    print("=" * 40)
    
    builder = NSISBuilder()
    success = builder.build()
    
    if success:
        print("\n构建成功! 可以分发安装程序了。")
        print("\n使用说明:")
        print("1. 将生成的 LocalQA_Setup_1.0.0.exe 分发给用户")
        print("2. 用户运行安装程序即可完成安装")
        print("3. 安装后可通过桌面快捷方式启动程序")
    else:
        print("\n构建失败，请检查错误信息。")
        
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
