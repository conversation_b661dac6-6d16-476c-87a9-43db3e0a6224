#pragma once

// @generated by torchgen/gen.py from NativeMetaFunctions.h

#include <ATen/core/Tensor.h>
#include <ATen/core/IListRef.h>
#include <ATen/TensorMeta.h>
#include <ATen/TensorIterator.h>

#include <ATen/ops/_adaptive_avg_pool2d_meta.h>
#include <ATen/ops/_adaptive_avg_pool2d_backward_meta.h>
#include <ATen/ops/_adaptive_avg_pool3d_meta.h>
#include <ATen/ops/_adaptive_avg_pool3d_backward_meta.h>
#include <ATen/ops/_add_batch_dim_meta.h>
#include <ATen/ops/_add_relu_meta.h>
#include <ATen/ops/_addmm_activation_meta.h>
#include <ATen/ops/_aminmax_meta.h>
#include <ATen/ops/_amp_foreach_non_finite_check_and_unscale_meta.h>
#include <ATen/ops/_amp_update_scale_meta.h>
#include <ATen/ops/_assert_async_meta.h>
#include <ATen/ops/_assert_tensor_metadata_meta.h>
#include <ATen/ops/_autocast_to_full_precision_meta.h>
#include <ATen/ops/_autocast_to_reduced_precision_meta.h>
#include <ATen/ops/_backward_meta.h>
#include <ATen/ops/_batch_norm_impl_index_meta.h>
#include <ATen/ops/_batch_norm_impl_index_backward_meta.h>
#include <ATen/ops/_cast_Byte_meta.h>
#include <ATen/ops/_cast_Char_meta.h>
#include <ATen/ops/_cast_Double_meta.h>
#include <ATen/ops/_cast_Float_meta.h>
#include <ATen/ops/_cast_Half_meta.h>
#include <ATen/ops/_cast_Int_meta.h>
#include <ATen/ops/_cast_Long_meta.h>
#include <ATen/ops/_cast_Short_meta.h>
#include <ATen/ops/_cdist_backward_meta.h>
#include <ATen/ops/_cdist_forward_meta.h>
#include <ATen/ops/_cholesky_solve_helper_meta.h>
#include <ATen/ops/_choose_qparams_per_tensor_meta.h>
#include <ATen/ops/_coalesce_meta.h>
#include <ATen/ops/_coalesced_meta.h>
#include <ATen/ops/_compute_linear_combination_meta.h>
#include <ATen/ops/_conj_meta.h>
#include <ATen/ops/_conj_copy_meta.h>
#include <ATen/ops/_conj_physical_meta.h>
#include <ATen/ops/_conv_depthwise2d_meta.h>
#include <ATen/ops/_convert_indices_from_coo_to_csr_meta.h>
#include <ATen/ops/_convert_indices_from_csr_to_coo_meta.h>
#include <ATen/ops/_convert_weight_to_int4pack_meta.h>
#include <ATen/ops/_convolution_meta.h>
#include <ATen/ops/_convolution_double_backward_meta.h>
#include <ATen/ops/_convolution_mode_meta.h>
#include <ATen/ops/_copy_from_meta.h>
#include <ATen/ops/_copy_from_and_resize_meta.h>
#include <ATen/ops/_cslt_compress_meta.h>
#include <ATen/ops/_cslt_sparse_mm_meta.h>
#include <ATen/ops/_ctc_loss_meta.h>
#include <ATen/ops/_ctc_loss_backward_meta.h>
#include <ATen/ops/_cudnn_ctc_loss_meta.h>
#include <ATen/ops/_cudnn_init_dropout_state_meta.h>
#include <ATen/ops/_cudnn_rnn_meta.h>
#include <ATen/ops/_cudnn_rnn_backward_meta.h>
#include <ATen/ops/_cudnn_rnn_flatten_weight_meta.h>
#include <ATen/ops/_cufft_clear_plan_cache_meta.h>
#include <ATen/ops/_cufft_get_plan_cache_max_size_meta.h>
#include <ATen/ops/_cufft_get_plan_cache_size_meta.h>
#include <ATen/ops/_cufft_set_plan_cache_max_size_meta.h>
#include <ATen/ops/_cummax_helper_meta.h>
#include <ATen/ops/_cummin_helper_meta.h>
#include <ATen/ops/_debug_has_internal_overlap_meta.h>
#include <ATen/ops/_dimI_meta.h>
#include <ATen/ops/_dimV_meta.h>
#include <ATen/ops/_dim_arange_meta.h>
#include <ATen/ops/_dirichlet_grad_meta.h>
#include <ATen/ops/_efficient_attention_backward_meta.h>
#include <ATen/ops/_efficient_attention_forward_meta.h>
#include <ATen/ops/_efficientzerotensor_meta.h>
#include <ATen/ops/_embedding_bag_meta.h>
#include <ATen/ops/_embedding_bag_backward_meta.h>
#include <ATen/ops/_embedding_bag_dense_backward_meta.h>
#include <ATen/ops/_embedding_bag_forward_only_meta.h>
#include <ATen/ops/_embedding_bag_per_sample_weights_backward_meta.h>
#include <ATen/ops/_embedding_bag_sparse_backward_meta.h>
#include <ATen/ops/_empty_affine_quantized_meta.h>
#include <ATen/ops/_empty_per_channel_affine_quantized_meta.h>
#include <ATen/ops/_euclidean_dist_meta.h>
#include <ATen/ops/_fake_quantize_learnable_per_channel_affine_meta.h>
#include <ATen/ops/_fake_quantize_learnable_per_channel_affine_backward_meta.h>
#include <ATen/ops/_fake_quantize_learnable_per_tensor_affine_meta.h>
#include <ATen/ops/_fake_quantize_learnable_per_tensor_affine_backward_meta.h>
#include <ATen/ops/_fake_quantize_per_tensor_affine_cachemask_tensor_qparams_meta.h>
#include <ATen/ops/_fft_c2c_meta.h>
#include <ATen/ops/_fft_c2r_meta.h>
#include <ATen/ops/_fft_r2c_meta.h>
#include <ATen/ops/_fill_mem_eff_dropout_mask_meta.h>
#include <ATen/ops/_flash_attention_backward_meta.h>
#include <ATen/ops/_flash_attention_forward_meta.h>
#include <ATen/ops/_foobar_meta.h>
#include <ATen/ops/_foreach_abs_meta.h>
#include <ATen/ops/_foreach_acos_meta.h>
#include <ATen/ops/_foreach_add_meta.h>
#include <ATen/ops/_foreach_addcdiv_meta.h>
#include <ATen/ops/_foreach_addcmul_meta.h>
#include <ATen/ops/_foreach_asin_meta.h>
#include <ATen/ops/_foreach_atan_meta.h>
#include <ATen/ops/_foreach_ceil_meta.h>
#include <ATen/ops/_foreach_clamp_max_meta.h>
#include <ATen/ops/_foreach_clamp_min_meta.h>
#include <ATen/ops/_foreach_copy_meta.h>
#include <ATen/ops/_foreach_cos_meta.h>
#include <ATen/ops/_foreach_cosh_meta.h>
#include <ATen/ops/_foreach_div_meta.h>
#include <ATen/ops/_foreach_erf_meta.h>
#include <ATen/ops/_foreach_erfc_meta.h>
#include <ATen/ops/_foreach_exp_meta.h>
#include <ATen/ops/_foreach_expm1_meta.h>
#include <ATen/ops/_foreach_floor_meta.h>
#include <ATen/ops/_foreach_frac_meta.h>
#include <ATen/ops/_foreach_lerp_meta.h>
#include <ATen/ops/_foreach_lgamma_meta.h>
#include <ATen/ops/_foreach_log_meta.h>
#include <ATen/ops/_foreach_log10_meta.h>
#include <ATen/ops/_foreach_log1p_meta.h>
#include <ATen/ops/_foreach_log2_meta.h>
#include <ATen/ops/_foreach_maximum_meta.h>
#include <ATen/ops/_foreach_minimum_meta.h>
#include <ATen/ops/_foreach_mul_meta.h>
#include <ATen/ops/_foreach_neg_meta.h>
#include <ATen/ops/_foreach_norm_meta.h>
#include <ATen/ops/_foreach_pow_meta.h>
#include <ATen/ops/_foreach_reciprocal_meta.h>
#include <ATen/ops/_foreach_round_meta.h>
#include <ATen/ops/_foreach_sigmoid_meta.h>
#include <ATen/ops/_foreach_sign_meta.h>
#include <ATen/ops/_foreach_sin_meta.h>
#include <ATen/ops/_foreach_sinh_meta.h>
#include <ATen/ops/_foreach_sqrt_meta.h>
#include <ATen/ops/_foreach_sub_meta.h>
#include <ATen/ops/_foreach_tan_meta.h>
#include <ATen/ops/_foreach_tanh_meta.h>
#include <ATen/ops/_foreach_trunc_meta.h>
#include <ATen/ops/_foreach_zero_meta.h>
#include <ATen/ops/_functional_assert_async_meta.h>
#include <ATen/ops/_functional_sym_constrain_range_meta.h>
#include <ATen/ops/_functional_sym_constrain_range_for_size_meta.h>
#include <ATen/ops/_fused_adam_meta.h>
#include <ATen/ops/_fused_adamw_meta.h>
#include <ATen/ops/_fused_dropout_meta.h>
#include <ATen/ops/_fused_moving_avg_obs_fq_helper_meta.h>
#include <ATen/ops/_fused_sdp_choice_meta.h>
#include <ATen/ops/_fw_primal_meta.h>
#include <ATen/ops/_fw_primal_copy_meta.h>
#include <ATen/ops/_gather_sparse_backward_meta.h>
#include <ATen/ops/_grid_sampler_2d_cpu_fallback_meta.h>
#include <ATen/ops/_grid_sampler_2d_cpu_fallback_backward_meta.h>
#include <ATen/ops/_has_compatible_shallow_copy_type_meta.h>
#include <ATen/ops/_has_same_storage_numel_meta.h>
#include <ATen/ops/_histogramdd_bin_edges_meta.h>
#include <ATen/ops/_histogramdd_from_bin_cts_meta.h>
#include <ATen/ops/_histogramdd_from_bin_tensors_meta.h>
#include <ATen/ops/_index_put_impl_meta.h>
#include <ATen/ops/_indices_meta.h>
#include <ATen/ops/_indices_copy_meta.h>
#include <ATen/ops/_int_mm_meta.h>
#include <ATen/ops/_is_all_true_meta.h>
#include <ATen/ops/_is_any_true_meta.h>
#include <ATen/ops/_is_zerotensor_meta.h>
#include <ATen/ops/_linalg_check_errors_meta.h>
#include <ATen/ops/_linalg_det_meta.h>
#include <ATen/ops/_linalg_eigh_meta.h>
#include <ATen/ops/_linalg_slogdet_meta.h>
#include <ATen/ops/_linalg_solve_ex_meta.h>
#include <ATen/ops/_linalg_svd_meta.h>
#include <ATen/ops/_local_scalar_dense_meta.h>
#include <ATen/ops/_log_softmax_meta.h>
#include <ATen/ops/_log_softmax_backward_data_meta.h>
#include <ATen/ops/_logcumsumexp_meta.h>
#include <ATen/ops/_lstm_mps_meta.h>
#include <ATen/ops/_lu_with_info_meta.h>
#include <ATen/ops/_make_dep_token_meta.h>
#include <ATen/ops/_make_dual_meta.h>
#include <ATen/ops/_make_dual_copy_meta.h>
#include <ATen/ops/_make_per_channel_quantized_tensor_meta.h>
#include <ATen/ops/_make_per_tensor_quantized_tensor_meta.h>
#include <ATen/ops/_masked_scale_meta.h>
#include <ATen/ops/_masked_softmax_meta.h>
#include <ATen/ops/_masked_softmax_backward_meta.h>
#include <ATen/ops/_mixed_dtypes_linear_meta.h>
#include <ATen/ops/_mkldnn_reshape_meta.h>
#include <ATen/ops/_mkldnn_transpose_meta.h>
#include <ATen/ops/_mps_convolution_meta.h>
#include <ATen/ops/_mps_convolution_transpose_meta.h>
#include <ATen/ops/_native_batch_norm_legit_meta.h>
#include <ATen/ops/_native_batch_norm_legit_no_training_meta.h>
#include <ATen/ops/_native_multi_head_attention_meta.h>
#include <ATen/ops/_neg_view_meta.h>
#include <ATen/ops/_neg_view_copy_meta.h>
#include <ATen/ops/_nested_from_padded_meta.h>
#include <ATen/ops/_nested_from_padded_and_nested_example_meta.h>
#include <ATen/ops/_nested_select_backward_meta.h>
#include <ATen/ops/_nested_sum_backward_meta.h>
#include <ATen/ops/_nested_tensor_from_mask_meta.h>
#include <ATen/ops/_nested_tensor_from_mask_left_aligned_meta.h>
#include <ATen/ops/_nested_tensor_from_tensor_list_meta.h>
#include <ATen/ops/_nested_tensor_size_meta.h>
#include <ATen/ops/_nested_tensor_softmax_with_shape_meta.h>
#include <ATen/ops/_nested_tensor_storage_offsets_meta.h>
#include <ATen/ops/_nested_tensor_strides_meta.h>
#include <ATen/ops/_nested_view_from_buffer_meta.h>
#include <ATen/ops/_nested_view_from_buffer_copy_meta.h>
#include <ATen/ops/_new_zeros_with_same_feature_meta_meta.h>
#include <ATen/ops/_nnpack_available_meta.h>
#include <ATen/ops/_nnpack_spatial_convolution_meta.h>
#include <ATen/ops/_nnz_meta.h>
#include <ATen/ops/_pack_padded_sequence_meta.h>
#include <ATen/ops/_pack_padded_sequence_backward_meta.h>
#include <ATen/ops/_pad_circular_meta.h>
#include <ATen/ops/_pad_enum_meta.h>
#include <ATen/ops/_pad_packed_sequence_meta.h>
#include <ATen/ops/_pdist_backward_meta.h>
#include <ATen/ops/_pdist_forward_meta.h>
#include <ATen/ops/_pin_memory_meta.h>
#include <ATen/ops/_prelu_kernel_meta.h>
#include <ATen/ops/_prelu_kernel_backward_meta.h>
#include <ATen/ops/_propagate_xla_data_meta.h>
#include <ATen/ops/_remove_batch_dim_meta.h>
#include <ATen/ops/_reshape_alias_meta.h>
#include <ATen/ops/_reshape_alias_copy_meta.h>
#include <ATen/ops/_reshape_copy_meta.h>
#include <ATen/ops/_reshape_from_tensor_meta.h>
#include <ATen/ops/_resize_output_meta.h>
#include <ATen/ops/_rowwise_prune_meta.h>
#include <ATen/ops/_sample_dirichlet_meta.h>
#include <ATen/ops/_saturate_weight_to_fp16_meta.h>
#include <ATen/ops/_scaled_dot_product_attention_math_meta.h>
#include <ATen/ops/_scaled_dot_product_efficient_attention_meta.h>
#include <ATen/ops/_scaled_dot_product_efficient_attention_backward_meta.h>
#include <ATen/ops/_scaled_dot_product_flash_attention_meta.h>
#include <ATen/ops/_scaled_dot_product_flash_attention_backward_meta.h>
#include <ATen/ops/_scaled_mm_meta.h>
#include <ATen/ops/_segment_reduce_backward_meta.h>
#include <ATen/ops/_shape_as_tensor_meta.h>
#include <ATen/ops/_slow_conv2d_backward_meta.h>
#include <ATen/ops/_slow_conv2d_forward_meta.h>
#include <ATen/ops/_sobol_engine_draw_meta.h>
#include <ATen/ops/_sobol_engine_ff_meta.h>
#include <ATen/ops/_sobol_engine_initialize_state_meta.h>
#include <ATen/ops/_sobol_engine_scramble_meta.h>
#include <ATen/ops/_softmax_meta.h>
#include <ATen/ops/_softmax_backward_data_meta.h>
#include <ATen/ops/_sparse_addmm_meta.h>
#include <ATen/ops/_sparse_broadcast_to_meta.h>
#include <ATen/ops/_sparse_broadcast_to_copy_meta.h>
#include <ATen/ops/_sparse_bsc_tensor_unsafe_meta.h>
#include <ATen/ops/_sparse_bsr_tensor_unsafe_meta.h>
#include <ATen/ops/_sparse_compressed_tensor_unsafe_meta.h>
#include <ATen/ops/_sparse_coo_tensor_unsafe_meta.h>
#include <ATen/ops/_sparse_coo_tensor_with_dims_meta.h>
#include <ATen/ops/_sparse_coo_tensor_with_dims_and_tensors_meta.h>
#include <ATen/ops/_sparse_csc_tensor_unsafe_meta.h>
#include <ATen/ops/_sparse_csr_prod_meta.h>
#include <ATen/ops/_sparse_csr_sum_meta.h>
#include <ATen/ops/_sparse_csr_tensor_unsafe_meta.h>
#include <ATen/ops/_sparse_log_softmax_meta.h>
#include <ATen/ops/_sparse_log_softmax_backward_data_meta.h>
#include <ATen/ops/_sparse_mask_projection_meta.h>
#include <ATen/ops/_sparse_mm_meta.h>
#include <ATen/ops/_sparse_mm_reduce_impl_meta.h>
#include <ATen/ops/_sparse_mm_reduce_impl_backward_meta.h>
#include <ATen/ops/_sparse_semi_structured_linear_meta.h>
#include <ATen/ops/_sparse_softmax_meta.h>
#include <ATen/ops/_sparse_softmax_backward_data_meta.h>
#include <ATen/ops/_sparse_sparse_matmul_meta.h>
#include <ATen/ops/_sparse_sum_meta.h>
#include <ATen/ops/_sparse_sum_backward_meta.h>
#include <ATen/ops/_spdiags_meta.h>
#include <ATen/ops/_stack_meta.h>
#include <ATen/ops/_standard_gamma_meta.h>
#include <ATen/ops/_standard_gamma_grad_meta.h>
#include <ATen/ops/_test_ambiguous_defaults_meta.h>
#include <ATen/ops/_test_autograd_multiple_dispatch_meta.h>
#include <ATen/ops/_test_autograd_multiple_dispatch_view_meta.h>
#include <ATen/ops/_test_autograd_multiple_dispatch_view_copy_meta.h>
#include <ATen/ops/_test_check_tensor_meta.h>
#include <ATen/ops/_test_functorch_fallback_meta.h>
#include <ATen/ops/_test_optional_filled_intlist_meta.h>
#include <ATen/ops/_test_optional_floatlist_meta.h>
#include <ATen/ops/_test_optional_intlist_meta.h>
#include <ATen/ops/_test_serialization_subcmul_meta.h>
#include <ATen/ops/_test_string_default_meta.h>
#include <ATen/ops/_test_warn_in_autograd_meta.h>
#include <ATen/ops/_thnn_differentiable_gru_cell_backward_meta.h>
#include <ATen/ops/_thnn_differentiable_lstm_cell_backward_meta.h>
#include <ATen/ops/_thnn_fused_gru_cell_meta.h>
#include <ATen/ops/_thnn_fused_gru_cell_backward_meta.h>
#include <ATen/ops/_thnn_fused_lstm_cell_meta.h>
#include <ATen/ops/_thnn_fused_lstm_cell_backward_meta.h>
#include <ATen/ops/_thnn_fused_lstm_cell_backward_impl_meta.h>
#include <ATen/ops/_to_copy_meta.h>
#include <ATen/ops/_to_cpu_meta.h>
#include <ATen/ops/_to_dense_meta.h>
#include <ATen/ops/_to_sparse_meta.h>
#include <ATen/ops/_to_sparse_bsc_meta.h>
#include <ATen/ops/_to_sparse_bsr_meta.h>
#include <ATen/ops/_to_sparse_csc_meta.h>
#include <ATen/ops/_to_sparse_csr_meta.h>
#include <ATen/ops/_to_sparse_semi_structured_meta.h>
#include <ATen/ops/_transform_bias_rescale_qkv_meta.h>
#include <ATen/ops/_transformer_encoder_layer_fwd_meta.h>
#include <ATen/ops/_trilinear_meta.h>
#include <ATen/ops/_triton_multi_head_attention_meta.h>
#include <ATen/ops/_triton_scaled_dot_attention_meta.h>
#include <ATen/ops/_unique_meta.h>
#include <ATen/ops/_unique2_meta.h>
#include <ATen/ops/_unpack_dual_meta.h>
#include <ATen/ops/_unsafe_index_meta.h>
#include <ATen/ops/_unsafe_index_put_meta.h>
#include <ATen/ops/_unsafe_view_meta.h>
#include <ATen/ops/_upsample_bicubic2d_aa_meta.h>
#include <ATen/ops/_upsample_bicubic2d_aa_backward_meta.h>
#include <ATen/ops/_upsample_bilinear2d_aa_meta.h>
#include <ATen/ops/_upsample_bilinear2d_aa_backward_meta.h>
#include <ATen/ops/_upsample_nearest_exact1d_meta.h>
#include <ATen/ops/_upsample_nearest_exact1d_backward_meta.h>
#include <ATen/ops/_upsample_nearest_exact2d_meta.h>
#include <ATen/ops/_upsample_nearest_exact2d_backward_meta.h>
#include <ATen/ops/_upsample_nearest_exact3d_meta.h>
#include <ATen/ops/_upsample_nearest_exact3d_backward_meta.h>
#include <ATen/ops/_use_cudnn_ctc_loss_meta.h>
#include <ATen/ops/_use_cudnn_rnn_flatten_weight_meta.h>
#include <ATen/ops/_validate_compressed_sparse_indices_meta.h>
#include <ATen/ops/_validate_sparse_bsc_tensor_args_meta.h>
#include <ATen/ops/_validate_sparse_bsr_tensor_args_meta.h>
#include <ATen/ops/_validate_sparse_compressed_tensor_args_meta.h>
#include <ATen/ops/_validate_sparse_coo_tensor_args_meta.h>
#include <ATen/ops/_validate_sparse_csc_tensor_args_meta.h>
#include <ATen/ops/_validate_sparse_csr_tensor_args_meta.h>
#include <ATen/ops/_values_meta.h>
#include <ATen/ops/_values_copy_meta.h>
#include <ATen/ops/_version_meta.h>
#include <ATen/ops/_weight_int4pack_mm_meta.h>
#include <ATen/ops/_weight_norm_meta.h>
#include <ATen/ops/_weight_norm_differentiable_backward_meta.h>
#include <ATen/ops/_weight_norm_interface_meta.h>
#include <ATen/ops/_weight_norm_interface_backward_meta.h>
#include <ATen/ops/abs_meta.h>
#include <ATen/ops/absolute_meta.h>
#include <ATen/ops/acos_meta.h>
#include <ATen/ops/acosh_meta.h>
#include <ATen/ops/adaptive_avg_pool1d_meta.h>
#include <ATen/ops/adaptive_avg_pool2d_meta.h>
#include <ATen/ops/adaptive_avg_pool3d_meta.h>
#include <ATen/ops/adaptive_avg_pool3d_backward_meta.h>
#include <ATen/ops/adaptive_max_pool1d_meta.h>
#include <ATen/ops/adaptive_max_pool2d_meta.h>
#include <ATen/ops/adaptive_max_pool2d_backward_meta.h>
#include <ATen/ops/adaptive_max_pool3d_meta.h>
#include <ATen/ops/adaptive_max_pool3d_backward_meta.h>
#include <ATen/ops/add_meta.h>
#include <ATen/ops/addbmm_meta.h>
#include <ATen/ops/addcdiv_meta.h>
#include <ATen/ops/addcmul_meta.h>
#include <ATen/ops/addmm_meta.h>
#include <ATen/ops/addmv_meta.h>
#include <ATen/ops/addr_meta.h>
#include <ATen/ops/adjoint_meta.h>
#include <ATen/ops/affine_grid_generator_meta.h>
#include <ATen/ops/affine_grid_generator_backward_meta.h>
#include <ATen/ops/alias_meta.h>
#include <ATen/ops/alias_copy_meta.h>
#include <ATen/ops/align_as_meta.h>
#include <ATen/ops/align_tensors_meta.h>
#include <ATen/ops/align_to_meta.h>
#include <ATen/ops/all_meta.h>
#include <ATen/ops/allclose_meta.h>
#include <ATen/ops/alpha_dropout_meta.h>
#include <ATen/ops/amax_meta.h>
#include <ATen/ops/amin_meta.h>
#include <ATen/ops/aminmax_meta.h>
#include <ATen/ops/and_meta.h>
#include <ATen/ops/angle_meta.h>
#include <ATen/ops/any_meta.h>
#include <ATen/ops/arange_meta.h>
#include <ATen/ops/arccos_meta.h>
#include <ATen/ops/arccosh_meta.h>
#include <ATen/ops/arcsin_meta.h>
#include <ATen/ops/arcsinh_meta.h>
#include <ATen/ops/arctan_meta.h>
#include <ATen/ops/arctan2_meta.h>
#include <ATen/ops/arctanh_meta.h>
#include <ATen/ops/argmax_meta.h>
#include <ATen/ops/argmin_meta.h>
#include <ATen/ops/argsort_meta.h>
#include <ATen/ops/argwhere_meta.h>
#include <ATen/ops/as_strided_meta.h>
#include <ATen/ops/as_strided_copy_meta.h>
#include <ATen/ops/as_strided_scatter_meta.h>
#include <ATen/ops/asin_meta.h>
#include <ATen/ops/asinh_meta.h>
#include <ATen/ops/atan_meta.h>
#include <ATen/ops/atan2_meta.h>
#include <ATen/ops/atanh_meta.h>
#include <ATen/ops/atleast_1d_meta.h>
#include <ATen/ops/atleast_2d_meta.h>
#include <ATen/ops/atleast_3d_meta.h>
#include <ATen/ops/avg_pool1d_meta.h>
#include <ATen/ops/avg_pool2d_meta.h>
#include <ATen/ops/avg_pool2d_backward_meta.h>
#include <ATen/ops/avg_pool3d_meta.h>
#include <ATen/ops/avg_pool3d_backward_meta.h>
#include <ATen/ops/baddbmm_meta.h>
#include <ATen/ops/bartlett_window_meta.h>
#include <ATen/ops/batch_norm_meta.h>
#include <ATen/ops/batch_norm_backward_elemt_meta.h>
#include <ATen/ops/batch_norm_backward_reduce_meta.h>
#include <ATen/ops/batch_norm_elemt_meta.h>
#include <ATen/ops/batch_norm_gather_stats_meta.h>
#include <ATen/ops/batch_norm_gather_stats_with_counts_meta.h>
#include <ATen/ops/batch_norm_stats_meta.h>
#include <ATen/ops/batch_norm_update_stats_meta.h>
#include <ATen/ops/bernoulli_meta.h>
#include <ATen/ops/bilinear_meta.h>
#include <ATen/ops/binary_cross_entropy_meta.h>
#include <ATen/ops/binary_cross_entropy_backward_meta.h>
#include <ATen/ops/binary_cross_entropy_with_logits_meta.h>
#include <ATen/ops/bincount_meta.h>
#include <ATen/ops/binomial_meta.h>
#include <ATen/ops/bitwise_and_meta.h>
#include <ATen/ops/bitwise_left_shift_meta.h>
#include <ATen/ops/bitwise_not_meta.h>
#include <ATen/ops/bitwise_or_meta.h>
#include <ATen/ops/bitwise_right_shift_meta.h>
#include <ATen/ops/bitwise_xor_meta.h>
#include <ATen/ops/blackman_window_meta.h>
#include <ATen/ops/block_diag_meta.h>
#include <ATen/ops/bmm_meta.h>
#include <ATen/ops/broadcast_tensors_meta.h>
#include <ATen/ops/broadcast_to_meta.h>
#include <ATen/ops/bucketize_meta.h>
#include <ATen/ops/can_cast_meta.h>
#include <ATen/ops/cartesian_prod_meta.h>
#include <ATen/ops/cat_meta.h>
#include <ATen/ops/cauchy_meta.h>
#include <ATen/ops/ccol_indices_meta.h>
#include <ATen/ops/ccol_indices_copy_meta.h>
#include <ATen/ops/cdist_meta.h>
#include <ATen/ops/ceil_meta.h>
#include <ATen/ops/celu_meta.h>
#include <ATen/ops/chain_matmul_meta.h>
#include <ATen/ops/chalf_meta.h>
#include <ATen/ops/channel_shuffle_meta.h>
#include <ATen/ops/cholesky_meta.h>
#include <ATen/ops/cholesky_inverse_meta.h>
#include <ATen/ops/cholesky_solve_meta.h>
#include <ATen/ops/choose_qparams_optimized_meta.h>
#include <ATen/ops/chunk_meta.h>
#include <ATen/ops/clamp_meta.h>
#include <ATen/ops/clamp_max_meta.h>
#include <ATen/ops/clamp_min_meta.h>
#include <ATen/ops/clip_meta.h>
#include <ATen/ops/clone_meta.h>
#include <ATen/ops/coalesce_meta.h>
#include <ATen/ops/col2im_meta.h>
#include <ATen/ops/col_indices_meta.h>
#include <ATen/ops/col_indices_copy_meta.h>
#include <ATen/ops/column_stack_meta.h>
#include <ATen/ops/combinations_meta.h>
#include <ATen/ops/complex_meta.h>
#include <ATen/ops/concat_meta.h>
#include <ATen/ops/concatenate_meta.h>
#include <ATen/ops/conj_meta.h>
#include <ATen/ops/conj_physical_meta.h>
#include <ATen/ops/constant_pad_nd_meta.h>
#include <ATen/ops/contiguous_meta.h>
#include <ATen/ops/conv1d_meta.h>
#include <ATen/ops/conv2d_meta.h>
#include <ATen/ops/conv3d_meta.h>
#include <ATen/ops/conv_depthwise3d_meta.h>
#include <ATen/ops/conv_tbc_meta.h>
#include <ATen/ops/conv_tbc_backward_meta.h>
#include <ATen/ops/conv_transpose1d_meta.h>
#include <ATen/ops/conv_transpose2d_meta.h>
#include <ATen/ops/conv_transpose3d_meta.h>
#include <ATen/ops/convolution_meta.h>
#include <ATen/ops/convolution_backward_meta.h>
#include <ATen/ops/convolution_backward_overrideable_meta.h>
#include <ATen/ops/convolution_overrideable_meta.h>
#include <ATen/ops/copy_meta.h>
#include <ATen/ops/copy_sparse_to_sparse_meta.h>
#include <ATen/ops/copysign_meta.h>
#include <ATen/ops/corrcoef_meta.h>
#include <ATen/ops/cos_meta.h>
#include <ATen/ops/cosh_meta.h>
#include <ATen/ops/cosine_embedding_loss_meta.h>
#include <ATen/ops/cosine_similarity_meta.h>
#include <ATen/ops/count_nonzero_meta.h>
#include <ATen/ops/cov_meta.h>
#include <ATen/ops/cross_meta.h>
#include <ATen/ops/cross_entropy_loss_meta.h>
#include <ATen/ops/crow_indices_meta.h>
#include <ATen/ops/crow_indices_copy_meta.h>
#include <ATen/ops/ctc_loss_meta.h>
#include <ATen/ops/cudnn_affine_grid_generator_meta.h>
#include <ATen/ops/cudnn_affine_grid_generator_backward_meta.h>
#include <ATen/ops/cudnn_batch_norm_meta.h>
#include <ATen/ops/cudnn_batch_norm_backward_meta.h>
#include <ATen/ops/cudnn_convolution_meta.h>
#include <ATen/ops/cudnn_convolution_add_relu_meta.h>
#include <ATen/ops/cudnn_convolution_relu_meta.h>
#include <ATen/ops/cudnn_convolution_transpose_meta.h>
#include <ATen/ops/cudnn_grid_sampler_meta.h>
#include <ATen/ops/cudnn_grid_sampler_backward_meta.h>
#include <ATen/ops/cudnn_is_acceptable_meta.h>
#include <ATen/ops/cummax_meta.h>
#include <ATen/ops/cummaxmin_backward_meta.h>
#include <ATen/ops/cummin_meta.h>
#include <ATen/ops/cumprod_meta.h>
#include <ATen/ops/cumprod_backward_meta.h>
#include <ATen/ops/cumsum_meta.h>
#include <ATen/ops/cumulative_trapezoid_meta.h>
#include <ATen/ops/data_meta.h>
#include <ATen/ops/deg2rad_meta.h>
#include <ATen/ops/dense_dim_meta.h>
#include <ATen/ops/dequantize_meta.h>
#include <ATen/ops/det_meta.h>
#include <ATen/ops/detach_meta.h>
#include <ATen/ops/detach_copy_meta.h>
#include <ATen/ops/diag_meta.h>
#include <ATen/ops/diag_embed_meta.h>
#include <ATen/ops/diagflat_meta.h>
#include <ATen/ops/diagonal_meta.h>
#include <ATen/ops/diagonal_backward_meta.h>
#include <ATen/ops/diagonal_copy_meta.h>
#include <ATen/ops/diagonal_scatter_meta.h>
#include <ATen/ops/diff_meta.h>
#include <ATen/ops/digamma_meta.h>
#include <ATen/ops/dist_meta.h>
#include <ATen/ops/div_meta.h>
#include <ATen/ops/divide_meta.h>
#include <ATen/ops/dot_meta.h>
#include <ATen/ops/dropout_meta.h>
#include <ATen/ops/dsplit_meta.h>
#include <ATen/ops/dstack_meta.h>
#include <ATen/ops/einsum_meta.h>
#include <ATen/ops/elu_meta.h>
#include <ATen/ops/elu_backward_meta.h>
#include <ATen/ops/embedding_meta.h>
#include <ATen/ops/embedding_backward_meta.h>
#include <ATen/ops/embedding_bag_meta.h>
#include <ATen/ops/embedding_dense_backward_meta.h>
#include <ATen/ops/embedding_renorm_meta.h>
#include <ATen/ops/embedding_sparse_backward_meta.h>
#include <ATen/ops/empty_meta.h>
#include <ATen/ops/empty_like_meta.h>
#include <ATen/ops/empty_permuted_meta.h>
#include <ATen/ops/empty_quantized_meta.h>
#include <ATen/ops/empty_strided_meta.h>
#include <ATen/ops/eq_meta.h>
#include <ATen/ops/equal_meta.h>
#include <ATen/ops/erf_meta.h>
#include <ATen/ops/erfc_meta.h>
#include <ATen/ops/erfinv_meta.h>
#include <ATen/ops/exp_meta.h>
#include <ATen/ops/exp2_meta.h>
#include <ATen/ops/expand_meta.h>
#include <ATen/ops/expand_as_meta.h>
#include <ATen/ops/expand_copy_meta.h>
#include <ATen/ops/expm1_meta.h>
#include <ATen/ops/exponential_meta.h>
#include <ATen/ops/eye_meta.h>
#include <ATen/ops/fake_quantize_per_channel_affine_meta.h>
#include <ATen/ops/fake_quantize_per_channel_affine_cachemask_meta.h>
#include <ATen/ops/fake_quantize_per_channel_affine_cachemask_backward_meta.h>
#include <ATen/ops/fake_quantize_per_tensor_affine_meta.h>
#include <ATen/ops/fake_quantize_per_tensor_affine_cachemask_meta.h>
#include <ATen/ops/fake_quantize_per_tensor_affine_cachemask_backward_meta.h>
#include <ATen/ops/fbgemm_linear_fp16_weight_meta.h>
#include <ATen/ops/fbgemm_linear_fp16_weight_fp32_activation_meta.h>
#include <ATen/ops/fbgemm_linear_int8_weight_meta.h>
#include <ATen/ops/fbgemm_linear_int8_weight_fp32_activation_meta.h>
#include <ATen/ops/fbgemm_linear_quantize_weight_meta.h>
#include <ATen/ops/fbgemm_pack_gemm_matrix_fp16_meta.h>
#include <ATen/ops/fbgemm_pack_quantized_matrix_meta.h>
#include <ATen/ops/feature_alpha_dropout_meta.h>
#include <ATen/ops/feature_dropout_meta.h>
#include <ATen/ops/fft_fft_meta.h>
#include <ATen/ops/fft_fft2_meta.h>
#include <ATen/ops/fft_fftfreq_meta.h>
#include <ATen/ops/fft_fftn_meta.h>
#include <ATen/ops/fft_fftshift_meta.h>
#include <ATen/ops/fft_hfft_meta.h>
#include <ATen/ops/fft_hfft2_meta.h>
#include <ATen/ops/fft_hfftn_meta.h>
#include <ATen/ops/fft_ifft_meta.h>
#include <ATen/ops/fft_ifft2_meta.h>
#include <ATen/ops/fft_ifftn_meta.h>
#include <ATen/ops/fft_ifftshift_meta.h>
#include <ATen/ops/fft_ihfft_meta.h>
#include <ATen/ops/fft_ihfft2_meta.h>
#include <ATen/ops/fft_ihfftn_meta.h>
#include <ATen/ops/fft_irfft_meta.h>
#include <ATen/ops/fft_irfft2_meta.h>
#include <ATen/ops/fft_irfftn_meta.h>
#include <ATen/ops/fft_rfft_meta.h>
#include <ATen/ops/fft_rfft2_meta.h>
#include <ATen/ops/fft_rfftfreq_meta.h>
#include <ATen/ops/fft_rfftn_meta.h>
#include <ATen/ops/fill_meta.h>
#include <ATen/ops/fill_diagonal_meta.h>
#include <ATen/ops/fix_meta.h>
#include <ATen/ops/flatten_meta.h>
#include <ATen/ops/flatten_dense_tensors_meta.h>
#include <ATen/ops/flip_meta.h>
#include <ATen/ops/fliplr_meta.h>
#include <ATen/ops/flipud_meta.h>
#include <ATen/ops/float_power_meta.h>
#include <ATen/ops/floor_meta.h>
#include <ATen/ops/floor_divide_meta.h>
#include <ATen/ops/fmax_meta.h>
#include <ATen/ops/fmin_meta.h>
#include <ATen/ops/fmod_meta.h>
#include <ATen/ops/frac_meta.h>
#include <ATen/ops/fractional_max_pool2d_meta.h>
#include <ATen/ops/fractional_max_pool2d_backward_meta.h>
#include <ATen/ops/fractional_max_pool3d_meta.h>
#include <ATen/ops/fractional_max_pool3d_backward_meta.h>
#include <ATen/ops/frexp_meta.h>
#include <ATen/ops/frobenius_norm_meta.h>
#include <ATen/ops/from_file_meta.h>
#include <ATen/ops/full_meta.h>
#include <ATen/ops/full_like_meta.h>
#include <ATen/ops/fused_moving_avg_obs_fake_quant_meta.h>
#include <ATen/ops/gather_meta.h>
#include <ATen/ops/gather_backward_meta.h>
#include <ATen/ops/gcd_meta.h>
#include <ATen/ops/ge_meta.h>
#include <ATen/ops/gelu_meta.h>
#include <ATen/ops/gelu_backward_meta.h>
#include <ATen/ops/geometric_meta.h>
#include <ATen/ops/geqrf_meta.h>
#include <ATen/ops/ger_meta.h>
#include <ATen/ops/glu_meta.h>
#include <ATen/ops/glu_backward_meta.h>
#include <ATen/ops/glu_backward_jvp_meta.h>
#include <ATen/ops/glu_jvp_meta.h>
#include <ATen/ops/gradient_meta.h>
#include <ATen/ops/greater_meta.h>
#include <ATen/ops/greater_equal_meta.h>
#include <ATen/ops/grid_sampler_meta.h>
#include <ATen/ops/grid_sampler_2d_meta.h>
#include <ATen/ops/grid_sampler_2d_backward_meta.h>
#include <ATen/ops/grid_sampler_3d_meta.h>
#include <ATen/ops/grid_sampler_3d_backward_meta.h>
#include <ATen/ops/group_norm_meta.h>
#include <ATen/ops/gru_meta.h>
#include <ATen/ops/gru_cell_meta.h>
#include <ATen/ops/gt_meta.h>
#include <ATen/ops/hamming_window_meta.h>
#include <ATen/ops/hann_window_meta.h>
#include <ATen/ops/hardshrink_meta.h>
#include <ATen/ops/hardshrink_backward_meta.h>
#include <ATen/ops/hardsigmoid_meta.h>
#include <ATen/ops/hardsigmoid_backward_meta.h>
#include <ATen/ops/hardswish_meta.h>
#include <ATen/ops/hardswish_backward_meta.h>
#include <ATen/ops/hardtanh_meta.h>
#include <ATen/ops/hardtanh_backward_meta.h>
#include <ATen/ops/heaviside_meta.h>
#include <ATen/ops/hinge_embedding_loss_meta.h>
#include <ATen/ops/histc_meta.h>
#include <ATen/ops/histogram_meta.h>
#include <ATen/ops/histogramdd_meta.h>
#include <ATen/ops/hsplit_meta.h>
#include <ATen/ops/hspmm_meta.h>
#include <ATen/ops/hstack_meta.h>
#include <ATen/ops/huber_loss_meta.h>
#include <ATen/ops/huber_loss_backward_meta.h>
#include <ATen/ops/hypot_meta.h>
#include <ATen/ops/i0_meta.h>
#include <ATen/ops/igamma_meta.h>
#include <ATen/ops/igammac_meta.h>
#include <ATen/ops/im2col_meta.h>
#include <ATen/ops/imag_meta.h>
#include <ATen/ops/index_meta.h>
#include <ATen/ops/index_add_meta.h>
#include <ATen/ops/index_copy_meta.h>
#include <ATen/ops/index_fill_meta.h>
#include <ATen/ops/index_put_meta.h>
#include <ATen/ops/index_reduce_meta.h>
#include <ATen/ops/index_select_meta.h>
#include <ATen/ops/index_select_backward_meta.h>
#include <ATen/ops/indices_meta.h>
#include <ATen/ops/indices_copy_meta.h>
#include <ATen/ops/infinitely_differentiable_gelu_backward_meta.h>
#include <ATen/ops/inner_meta.h>
#include <ATen/ops/instance_norm_meta.h>
#include <ATen/ops/int_repr_meta.h>
#include <ATen/ops/inverse_meta.h>
#include <ATen/ops/is_coalesced_meta.h>
#include <ATen/ops/is_complex_meta.h>
#include <ATen/ops/is_conj_meta.h>
#include <ATen/ops/is_distributed_meta.h>
#include <ATen/ops/is_floating_point_meta.h>
#include <ATen/ops/is_inference_meta.h>
#include <ATen/ops/is_leaf_meta.h>
#include <ATen/ops/is_neg_meta.h>
#include <ATen/ops/is_nonzero_meta.h>
#include <ATen/ops/is_pinned_meta.h>
#include <ATen/ops/is_same_size_meta.h>
#include <ATen/ops/is_set_to_meta.h>
#include <ATen/ops/is_signed_meta.h>
#include <ATen/ops/is_vulkan_available_meta.h>
#include <ATen/ops/isclose_meta.h>
#include <ATen/ops/isfinite_meta.h>
#include <ATen/ops/isin_meta.h>
#include <ATen/ops/isinf_meta.h>
#include <ATen/ops/isnan_meta.h>
#include <ATen/ops/isneginf_meta.h>
#include <ATen/ops/isposinf_meta.h>
#include <ATen/ops/isreal_meta.h>
#include <ATen/ops/istft_meta.h>
#include <ATen/ops/item_meta.h>
#include <ATen/ops/kaiser_window_meta.h>
#include <ATen/ops/kl_div_meta.h>
#include <ATen/ops/kron_meta.h>
#include <ATen/ops/kthvalue_meta.h>
#include <ATen/ops/l1_loss_meta.h>
#include <ATen/ops/layer_norm_meta.h>
#include <ATen/ops/lcm_meta.h>
#include <ATen/ops/ldexp_meta.h>
#include <ATen/ops/le_meta.h>
#include <ATen/ops/leaky_relu_meta.h>
#include <ATen/ops/leaky_relu_backward_meta.h>
#include <ATen/ops/lerp_meta.h>
#include <ATen/ops/less_meta.h>
#include <ATen/ops/less_equal_meta.h>
#include <ATen/ops/lgamma_meta.h>
#include <ATen/ops/lift_meta.h>
#include <ATen/ops/lift_fresh_meta.h>
#include <ATen/ops/lift_fresh_copy_meta.h>
#include <ATen/ops/linalg_cholesky_meta.h>
#include <ATen/ops/linalg_cholesky_ex_meta.h>
#include <ATen/ops/linalg_cond_meta.h>
#include <ATen/ops/linalg_cross_meta.h>
#include <ATen/ops/linalg_det_meta.h>
#include <ATen/ops/linalg_diagonal_meta.h>
#include <ATen/ops/linalg_eig_meta.h>
#include <ATen/ops/linalg_eigh_meta.h>
#include <ATen/ops/linalg_eigvals_meta.h>
#include <ATen/ops/linalg_eigvalsh_meta.h>
#include <ATen/ops/linalg_householder_product_meta.h>
#include <ATen/ops/linalg_inv_meta.h>
#include <ATen/ops/linalg_inv_ex_meta.h>
#include <ATen/ops/linalg_ldl_factor_meta.h>
#include <ATen/ops/linalg_ldl_factor_ex_meta.h>
#include <ATen/ops/linalg_ldl_solve_meta.h>
#include <ATen/ops/linalg_lstsq_meta.h>
#include <ATen/ops/linalg_lu_meta.h>
#include <ATen/ops/linalg_lu_factor_meta.h>
#include <ATen/ops/linalg_lu_factor_ex_meta.h>
#include <ATen/ops/linalg_lu_solve_meta.h>
#include <ATen/ops/linalg_matmul_meta.h>
#include <ATen/ops/linalg_matrix_exp_meta.h>
#include <ATen/ops/linalg_matrix_norm_meta.h>
#include <ATen/ops/linalg_matrix_power_meta.h>
#include <ATen/ops/linalg_matrix_rank_meta.h>
#include <ATen/ops/linalg_multi_dot_meta.h>
#include <ATen/ops/linalg_norm_meta.h>
#include <ATen/ops/linalg_pinv_meta.h>
#include <ATen/ops/linalg_qr_meta.h>
#include <ATen/ops/linalg_slogdet_meta.h>
#include <ATen/ops/linalg_solve_meta.h>
#include <ATen/ops/linalg_solve_ex_meta.h>
#include <ATen/ops/linalg_solve_triangular_meta.h>
#include <ATen/ops/linalg_svd_meta.h>
#include <ATen/ops/linalg_svdvals_meta.h>
#include <ATen/ops/linalg_tensorinv_meta.h>
#include <ATen/ops/linalg_tensorsolve_meta.h>
#include <ATen/ops/linalg_vander_meta.h>
#include <ATen/ops/linalg_vecdot_meta.h>
#include <ATen/ops/linalg_vector_norm_meta.h>
#include <ATen/ops/linear_meta.h>
#include <ATen/ops/linear_backward_meta.h>
#include <ATen/ops/linspace_meta.h>
#include <ATen/ops/log_meta.h>
#include <ATen/ops/log10_meta.h>
#include <ATen/ops/log1p_meta.h>
#include <ATen/ops/log2_meta.h>
#include <ATen/ops/log_normal_meta.h>
#include <ATen/ops/log_sigmoid_meta.h>
#include <ATen/ops/log_sigmoid_backward_meta.h>
#include <ATen/ops/log_sigmoid_forward_meta.h>
#include <ATen/ops/log_softmax_meta.h>
#include <ATen/ops/logaddexp_meta.h>
#include <ATen/ops/logaddexp2_meta.h>
#include <ATen/ops/logcumsumexp_meta.h>
#include <ATen/ops/logdet_meta.h>
#include <ATen/ops/logical_and_meta.h>
#include <ATen/ops/logical_not_meta.h>
#include <ATen/ops/logical_or_meta.h>
#include <ATen/ops/logical_xor_meta.h>
#include <ATen/ops/logit_meta.h>
#include <ATen/ops/logit_backward_meta.h>
#include <ATen/ops/logspace_meta.h>
#include <ATen/ops/logsumexp_meta.h>
#include <ATen/ops/lshift_meta.h>
#include <ATen/ops/lstm_meta.h>
#include <ATen/ops/lstm_cell_meta.h>
#include <ATen/ops/lstm_mps_backward_meta.h>
#include <ATen/ops/lt_meta.h>
#include <ATen/ops/lu_solve_meta.h>
#include <ATen/ops/lu_unpack_meta.h>
#include <ATen/ops/mH_meta.h>
#include <ATen/ops/mT_meta.h>
#include <ATen/ops/margin_ranking_loss_meta.h>
#include <ATen/ops/masked_fill_meta.h>
#include <ATen/ops/masked_scatter_meta.h>
#include <ATen/ops/masked_scatter_backward_meta.h>
#include <ATen/ops/masked_select_meta.h>
#include <ATen/ops/masked_select_backward_meta.h>
#include <ATen/ops/matmul_meta.h>
#include <ATen/ops/matmul_backward_meta.h>
#include <ATen/ops/matrix_H_meta.h>
#include <ATen/ops/matrix_exp_meta.h>
#include <ATen/ops/matrix_exp_backward_meta.h>
#include <ATen/ops/matrix_power_meta.h>
#include <ATen/ops/max_meta.h>
#include <ATen/ops/max_pool1d_meta.h>
#include <ATen/ops/max_pool1d_with_indices_meta.h>
#include <ATen/ops/max_pool2d_meta.h>
#include <ATen/ops/max_pool2d_backward_meta.h>
#include <ATen/ops/max_pool2d_with_indices_meta.h>
#include <ATen/ops/max_pool2d_with_indices_backward_meta.h>
#include <ATen/ops/max_pool3d_meta.h>
#include <ATen/ops/max_pool3d_with_indices_meta.h>
#include <ATen/ops/max_pool3d_with_indices_backward_meta.h>
#include <ATen/ops/max_unpool2d_meta.h>
#include <ATen/ops/max_unpool3d_meta.h>
#include <ATen/ops/maximum_meta.h>
#include <ATen/ops/mean_meta.h>
#include <ATen/ops/median_meta.h>
#include <ATen/ops/meshgrid_meta.h>
#include <ATen/ops/min_meta.h>
#include <ATen/ops/minimum_meta.h>
#include <ATen/ops/miopen_batch_norm_meta.h>
#include <ATen/ops/miopen_batch_norm_backward_meta.h>
#include <ATen/ops/miopen_convolution_meta.h>
#include <ATen/ops/miopen_convolution_add_relu_meta.h>
#include <ATen/ops/miopen_convolution_relu_meta.h>
#include <ATen/ops/miopen_convolution_transpose_meta.h>
#include <ATen/ops/miopen_depthwise_convolution_meta.h>
#include <ATen/ops/miopen_rnn_meta.h>
#include <ATen/ops/miopen_rnn_backward_meta.h>
#include <ATen/ops/mish_meta.h>
#include <ATen/ops/mish_backward_meta.h>
#include <ATen/ops/mkldnn_adaptive_avg_pool2d_meta.h>
#include <ATen/ops/mkldnn_adaptive_avg_pool2d_backward_meta.h>
#include <ATen/ops/mkldnn_convolution_meta.h>
#include <ATen/ops/mkldnn_linear_meta.h>
#include <ATen/ops/mkldnn_linear_backward_meta.h>
#include <ATen/ops/mkldnn_linear_backward_input_meta.h>
#include <ATen/ops/mkldnn_linear_backward_weights_meta.h>
#include <ATen/ops/mkldnn_max_pool2d_meta.h>
#include <ATen/ops/mkldnn_max_pool2d_backward_meta.h>
#include <ATen/ops/mkldnn_max_pool3d_meta.h>
#include <ATen/ops/mkldnn_max_pool3d_backward_meta.h>
#include <ATen/ops/mkldnn_reorder_conv2d_weight_meta.h>
#include <ATen/ops/mkldnn_reorder_conv3d_weight_meta.h>
#include <ATen/ops/mkldnn_rnn_layer_meta.h>
#include <ATen/ops/mkldnn_rnn_layer_backward_meta.h>
#include <ATen/ops/mm_meta.h>
#include <ATen/ops/mode_meta.h>
#include <ATen/ops/moveaxis_meta.h>
#include <ATen/ops/movedim_meta.h>
#include <ATen/ops/mps_convolution_backward_meta.h>
#include <ATen/ops/mps_convolution_transpose_backward_meta.h>
#include <ATen/ops/mse_loss_meta.h>
#include <ATen/ops/mse_loss_backward_meta.h>
#include <ATen/ops/msort_meta.h>
#include <ATen/ops/mul_meta.h>
#include <ATen/ops/multi_margin_loss_meta.h>
#include <ATen/ops/multi_margin_loss_backward_meta.h>
#include <ATen/ops/multilabel_margin_loss_meta.h>
#include <ATen/ops/multilabel_margin_loss_backward_meta.h>
#include <ATen/ops/multilabel_margin_loss_forward_meta.h>
#include <ATen/ops/multinomial_meta.h>
#include <ATen/ops/multiply_meta.h>
#include <ATen/ops/mv_meta.h>
#include <ATen/ops/mvlgamma_meta.h>
#include <ATen/ops/nan_to_num_meta.h>
#include <ATen/ops/nanmean_meta.h>
#include <ATen/ops/nanmedian_meta.h>
#include <ATen/ops/nanquantile_meta.h>
#include <ATen/ops/nansum_meta.h>
#include <ATen/ops/narrow_meta.h>
#include <ATen/ops/narrow_copy_meta.h>
#include <ATen/ops/native_batch_norm_meta.h>
#include <ATen/ops/native_batch_norm_backward_meta.h>
#include <ATen/ops/native_channel_shuffle_meta.h>
#include <ATen/ops/native_dropout_meta.h>
#include <ATen/ops/native_dropout_backward_meta.h>
#include <ATen/ops/native_group_norm_meta.h>
#include <ATen/ops/native_group_norm_backward_meta.h>
#include <ATen/ops/native_layer_norm_meta.h>
#include <ATen/ops/native_layer_norm_backward_meta.h>
#include <ATen/ops/native_norm_meta.h>
#include <ATen/ops/ne_meta.h>
#include <ATen/ops/neg_meta.h>
#include <ATen/ops/negative_meta.h>
#include <ATen/ops/nested_to_padded_tensor_meta.h>
#include <ATen/ops/new_empty_meta.h>
#include <ATen/ops/new_empty_strided_meta.h>
#include <ATen/ops/new_full_meta.h>
#include <ATen/ops/new_ones_meta.h>
#include <ATen/ops/new_zeros_meta.h>
#include <ATen/ops/nextafter_meta.h>
#include <ATen/ops/nll_loss_meta.h>
#include <ATen/ops/nll_loss2d_meta.h>
#include <ATen/ops/nll_loss2d_backward_meta.h>
#include <ATen/ops/nll_loss2d_forward_meta.h>
#include <ATen/ops/nll_loss_backward_meta.h>
#include <ATen/ops/nll_loss_forward_meta.h>
#include <ATen/ops/nll_loss_nd_meta.h>
#include <ATen/ops/nonzero_meta.h>
#include <ATen/ops/nonzero_numpy_meta.h>
#include <ATen/ops/nonzero_static_meta.h>
#include <ATen/ops/norm_meta.h>
#include <ATen/ops/norm_except_dim_meta.h>
#include <ATen/ops/normal_meta.h>
#include <ATen/ops/not_equal_meta.h>
#include <ATen/ops/nuclear_norm_meta.h>
#include <ATen/ops/numpy_T_meta.h>
#include <ATen/ops/one_hot_meta.h>
#include <ATen/ops/ones_meta.h>
#include <ATen/ops/ones_like_meta.h>
#include <ATen/ops/or_meta.h>
#include <ATen/ops/orgqr_meta.h>
#include <ATen/ops/ormqr_meta.h>
#include <ATen/ops/outer_meta.h>
#include <ATen/ops/output_nr_meta.h>
#include <ATen/ops/pad_meta.h>
#include <ATen/ops/pad_sequence_meta.h>
#include <ATen/ops/pairwise_distance_meta.h>
#include <ATen/ops/pdist_meta.h>
#include <ATen/ops/permute_meta.h>
#include <ATen/ops/permute_copy_meta.h>
#include <ATen/ops/pin_memory_meta.h>
#include <ATen/ops/pinverse_meta.h>
#include <ATen/ops/pixel_shuffle_meta.h>
#include <ATen/ops/pixel_unshuffle_meta.h>
#include <ATen/ops/poisson_meta.h>
#include <ATen/ops/poisson_nll_loss_meta.h>
#include <ATen/ops/polar_meta.h>
#include <ATen/ops/polygamma_meta.h>
#include <ATen/ops/positive_meta.h>
#include <ATen/ops/pow_meta.h>
#include <ATen/ops/prelu_meta.h>
#include <ATen/ops/prod_meta.h>
#include <ATen/ops/promote_types_meta.h>
#include <ATen/ops/put_meta.h>
#include <ATen/ops/q_per_channel_axis_meta.h>
#include <ATen/ops/q_per_channel_scales_meta.h>
#include <ATen/ops/q_per_channel_zero_points_meta.h>
#include <ATen/ops/q_scale_meta.h>
#include <ATen/ops/q_zero_point_meta.h>
#include <ATen/ops/qr_meta.h>
#include <ATen/ops/qscheme_meta.h>
#include <ATen/ops/quantile_meta.h>
#include <ATen/ops/quantize_per_channel_meta.h>
#include <ATen/ops/quantize_per_tensor_meta.h>
#include <ATen/ops/quantize_per_tensor_dynamic_meta.h>
#include <ATen/ops/quantized_batch_norm_meta.h>
#include <ATen/ops/quantized_gru_cell_meta.h>
#include <ATen/ops/quantized_lstm_cell_meta.h>
#include <ATen/ops/quantized_max_pool1d_meta.h>
#include <ATen/ops/quantized_max_pool2d_meta.h>
#include <ATen/ops/quantized_max_pool3d_meta.h>
#include <ATen/ops/quantized_rnn_relu_cell_meta.h>
#include <ATen/ops/quantized_rnn_tanh_cell_meta.h>
#include <ATen/ops/rad2deg_meta.h>
#include <ATen/ops/rand_meta.h>
#include <ATen/ops/rand_like_meta.h>
#include <ATen/ops/randint_meta.h>
#include <ATen/ops/randint_like_meta.h>
#include <ATen/ops/randn_meta.h>
#include <ATen/ops/randn_like_meta.h>
#include <ATen/ops/random_meta.h>
#include <ATen/ops/randperm_meta.h>
#include <ATen/ops/range_meta.h>
#include <ATen/ops/ravel_meta.h>
#include <ATen/ops/real_meta.h>
#include <ATen/ops/reciprocal_meta.h>
#include <ATen/ops/record_stream_meta.h>
#include <ATen/ops/refine_names_meta.h>
#include <ATen/ops/reflection_pad1d_meta.h>
#include <ATen/ops/reflection_pad1d_backward_meta.h>
#include <ATen/ops/reflection_pad2d_meta.h>
#include <ATen/ops/reflection_pad2d_backward_meta.h>
#include <ATen/ops/reflection_pad3d_meta.h>
#include <ATen/ops/reflection_pad3d_backward_meta.h>
#include <ATen/ops/relu_meta.h>
#include <ATen/ops/relu6_meta.h>
#include <ATen/ops/remainder_meta.h>
#include <ATen/ops/rename_meta.h>
#include <ATen/ops/renorm_meta.h>
#include <ATen/ops/repeat_meta.h>
#include <ATen/ops/repeat_interleave_meta.h>
#include <ATen/ops/replication_pad1d_meta.h>
#include <ATen/ops/replication_pad1d_backward_meta.h>
#include <ATen/ops/replication_pad2d_meta.h>
#include <ATen/ops/replication_pad2d_backward_meta.h>
#include <ATen/ops/replication_pad3d_meta.h>
#include <ATen/ops/replication_pad3d_backward_meta.h>
#include <ATen/ops/requires_grad_meta.h>
#include <ATen/ops/reshape_meta.h>
#include <ATen/ops/reshape_as_meta.h>
#include <ATen/ops/resize_meta.h>
#include <ATen/ops/resize_as_meta.h>
#include <ATen/ops/resize_as_sparse_meta.h>
#include <ATen/ops/resolve_conj_meta.h>
#include <ATen/ops/resolve_neg_meta.h>
#include <ATen/ops/result_type_meta.h>
#include <ATen/ops/retain_grad_meta.h>
#include <ATen/ops/retains_grad_meta.h>
#include <ATen/ops/rnn_relu_meta.h>
#include <ATen/ops/rnn_relu_cell_meta.h>
#include <ATen/ops/rnn_tanh_meta.h>
#include <ATen/ops/rnn_tanh_cell_meta.h>
#include <ATen/ops/roll_meta.h>
#include <ATen/ops/rot90_meta.h>
#include <ATen/ops/round_meta.h>
#include <ATen/ops/row_indices_meta.h>
#include <ATen/ops/row_indices_copy_meta.h>
#include <ATen/ops/row_stack_meta.h>
#include <ATen/ops/rrelu_meta.h>
#include <ATen/ops/rrelu_with_noise_meta.h>
#include <ATen/ops/rrelu_with_noise_backward_meta.h>
#include <ATen/ops/rshift_meta.h>
#include <ATen/ops/rsqrt_meta.h>
#include <ATen/ops/rsub_meta.h>
#include <ATen/ops/scalar_tensor_meta.h>
#include <ATen/ops/scaled_dot_product_attention_meta.h>
#include <ATen/ops/scatter_meta.h>
#include <ATen/ops/scatter_add_meta.h>
#include <ATen/ops/scatter_reduce_meta.h>
#include <ATen/ops/searchsorted_meta.h>
#include <ATen/ops/segment_reduce_meta.h>
#include <ATen/ops/select_meta.h>
#include <ATen/ops/select_backward_meta.h>
#include <ATen/ops/select_copy_meta.h>
#include <ATen/ops/select_scatter_meta.h>
#include <ATen/ops/selu_meta.h>
#include <ATen/ops/set_meta.h>
#include <ATen/ops/set_data_meta.h>
#include <ATen/ops/sgn_meta.h>
#include <ATen/ops/sigmoid_meta.h>
#include <ATen/ops/sigmoid_backward_meta.h>
#include <ATen/ops/sign_meta.h>
#include <ATen/ops/signbit_meta.h>
#include <ATen/ops/silu_meta.h>
#include <ATen/ops/silu_backward_meta.h>
#include <ATen/ops/sin_meta.h>
#include <ATen/ops/sinc_meta.h>
#include <ATen/ops/sinh_meta.h>
#include <ATen/ops/size_meta.h>
#include <ATen/ops/slice_meta.h>
#include <ATen/ops/slice_backward_meta.h>
#include <ATen/ops/slice_copy_meta.h>
#include <ATen/ops/slice_scatter_meta.h>
#include <ATen/ops/slogdet_meta.h>
#include <ATen/ops/slow_conv3d_meta.h>
#include <ATen/ops/slow_conv3d_forward_meta.h>
#include <ATen/ops/slow_conv_dilated2d_meta.h>
#include <ATen/ops/slow_conv_dilated3d_meta.h>
#include <ATen/ops/slow_conv_transpose2d_meta.h>
#include <ATen/ops/slow_conv_transpose3d_meta.h>
#include <ATen/ops/smm_meta.h>
#include <ATen/ops/smooth_l1_loss_meta.h>
#include <ATen/ops/smooth_l1_loss_backward_meta.h>
#include <ATen/ops/soft_margin_loss_meta.h>
#include <ATen/ops/soft_margin_loss_backward_meta.h>
#include <ATen/ops/softmax_meta.h>
#include <ATen/ops/softplus_meta.h>
#include <ATen/ops/softplus_backward_meta.h>
#include <ATen/ops/softshrink_meta.h>
#include <ATen/ops/softshrink_backward_meta.h>
#include <ATen/ops/sort_meta.h>
#include <ATen/ops/sparse_bsc_tensor_meta.h>
#include <ATen/ops/sparse_bsr_tensor_meta.h>
#include <ATen/ops/sparse_compressed_tensor_meta.h>
#include <ATen/ops/sparse_coo_tensor_meta.h>
#include <ATen/ops/sparse_csc_tensor_meta.h>
#include <ATen/ops/sparse_csr_tensor_meta.h>
#include <ATen/ops/sparse_dim_meta.h>
#include <ATen/ops/sparse_mask_meta.h>
#include <ATen/ops/sparse_resize_meta.h>
#include <ATen/ops/sparse_resize_and_clear_meta.h>
#include <ATen/ops/sparse_sampled_addmm_meta.h>
#include <ATen/ops/special_airy_ai_meta.h>
#include <ATen/ops/special_bessel_j0_meta.h>
#include <ATen/ops/special_bessel_j1_meta.h>
#include <ATen/ops/special_bessel_y0_meta.h>
#include <ATen/ops/special_bessel_y1_meta.h>
#include <ATen/ops/special_chebyshev_polynomial_t_meta.h>
#include <ATen/ops/special_chebyshev_polynomial_u_meta.h>
#include <ATen/ops/special_chebyshev_polynomial_v_meta.h>
#include <ATen/ops/special_chebyshev_polynomial_w_meta.h>
#include <ATen/ops/special_digamma_meta.h>
#include <ATen/ops/special_entr_meta.h>
#include <ATen/ops/special_erf_meta.h>
#include <ATen/ops/special_erfc_meta.h>
#include <ATen/ops/special_erfcx_meta.h>
#include <ATen/ops/special_erfinv_meta.h>
#include <ATen/ops/special_exp2_meta.h>
#include <ATen/ops/special_expit_meta.h>
#include <ATen/ops/special_expm1_meta.h>
#include <ATen/ops/special_gammainc_meta.h>
#include <ATen/ops/special_gammaincc_meta.h>
#include <ATen/ops/special_gammaln_meta.h>
#include <ATen/ops/special_hermite_polynomial_h_meta.h>
#include <ATen/ops/special_hermite_polynomial_he_meta.h>
#include <ATen/ops/special_i0_meta.h>
#include <ATen/ops/special_i0e_meta.h>
#include <ATen/ops/special_i1_meta.h>
#include <ATen/ops/special_i1e_meta.h>
#include <ATen/ops/special_laguerre_polynomial_l_meta.h>
#include <ATen/ops/special_legendre_polynomial_p_meta.h>
#include <ATen/ops/special_log1p_meta.h>
#include <ATen/ops/special_log_ndtr_meta.h>
#include <ATen/ops/special_log_softmax_meta.h>
#include <ATen/ops/special_logit_meta.h>
#include <ATen/ops/special_logsumexp_meta.h>
#include <ATen/ops/special_modified_bessel_i0_meta.h>
#include <ATen/ops/special_modified_bessel_i1_meta.h>
#include <ATen/ops/special_modified_bessel_k0_meta.h>
#include <ATen/ops/special_modified_bessel_k1_meta.h>
#include <ATen/ops/special_multigammaln_meta.h>
#include <ATen/ops/special_ndtr_meta.h>
#include <ATen/ops/special_ndtri_meta.h>
#include <ATen/ops/special_polygamma_meta.h>
#include <ATen/ops/special_psi_meta.h>
#include <ATen/ops/special_round_meta.h>
#include <ATen/ops/special_scaled_modified_bessel_k0_meta.h>
#include <ATen/ops/special_scaled_modified_bessel_k1_meta.h>
#include <ATen/ops/special_shifted_chebyshev_polynomial_t_meta.h>
#include <ATen/ops/special_shifted_chebyshev_polynomial_u_meta.h>
#include <ATen/ops/special_shifted_chebyshev_polynomial_v_meta.h>
#include <ATen/ops/special_shifted_chebyshev_polynomial_w_meta.h>
#include <ATen/ops/special_sinc_meta.h>
#include <ATen/ops/special_softmax_meta.h>
#include <ATen/ops/special_spherical_bessel_j0_meta.h>
#include <ATen/ops/special_xlog1py_meta.h>
#include <ATen/ops/special_xlogy_meta.h>
#include <ATen/ops/special_zeta_meta.h>
#include <ATen/ops/split_meta.h>
#include <ATen/ops/split_copy_meta.h>
#include <ATen/ops/split_with_sizes_meta.h>
#include <ATen/ops/split_with_sizes_copy_meta.h>
#include <ATen/ops/sqrt_meta.h>
#include <ATen/ops/square_meta.h>
#include <ATen/ops/squeeze_meta.h>
#include <ATen/ops/squeeze_copy_meta.h>
#include <ATen/ops/sspaddmm_meta.h>
#include <ATen/ops/stack_meta.h>
#include <ATen/ops/std_meta.h>
#include <ATen/ops/std_mean_meta.h>
#include <ATen/ops/stft_meta.h>
#include <ATen/ops/stride_meta.h>
#include <ATen/ops/sub_meta.h>
#include <ATen/ops/subtract_meta.h>
#include <ATen/ops/sum_meta.h>
#include <ATen/ops/sum_to_size_meta.h>
#include <ATen/ops/svd_meta.h>
#include <ATen/ops/swapaxes_meta.h>
#include <ATen/ops/swapdims_meta.h>
#include <ATen/ops/sym_constrain_range_meta.h>
#include <ATen/ops/sym_constrain_range_for_size_meta.h>
#include <ATen/ops/sym_numel_meta.h>
#include <ATen/ops/sym_size_meta.h>
#include <ATen/ops/sym_storage_offset_meta.h>
#include <ATen/ops/sym_stride_meta.h>
#include <ATen/ops/t_meta.h>
#include <ATen/ops/t_copy_meta.h>
#include <ATen/ops/take_meta.h>
#include <ATen/ops/take_along_dim_meta.h>
#include <ATen/ops/tan_meta.h>
#include <ATen/ops/tanh_meta.h>
#include <ATen/ops/tanh_backward_meta.h>
#include <ATen/ops/tensor_split_meta.h>
#include <ATen/ops/tensordot_meta.h>
#include <ATen/ops/thnn_conv2d_meta.h>
#include <ATen/ops/threshold_meta.h>
#include <ATen/ops/threshold_backward_meta.h>
#include <ATen/ops/tile_meta.h>
#include <ATen/ops/to_meta.h>
#include <ATen/ops/to_dense_meta.h>
#include <ATen/ops/to_dense_backward_meta.h>
#include <ATen/ops/to_mkldnn_meta.h>
#include <ATen/ops/to_mkldnn_backward_meta.h>
#include <ATen/ops/to_padded_tensor_meta.h>
#include <ATen/ops/to_sparse_meta.h>
#include <ATen/ops/to_sparse_bsc_meta.h>
#include <ATen/ops/to_sparse_bsr_meta.h>
#include <ATen/ops/to_sparse_csc_meta.h>
#include <ATen/ops/to_sparse_csr_meta.h>
#include <ATen/ops/topk_meta.h>
#include <ATen/ops/trace_meta.h>
#include <ATen/ops/trace_backward_meta.h>
#include <ATen/ops/transpose_meta.h>
#include <ATen/ops/transpose_copy_meta.h>
#include <ATen/ops/trapezoid_meta.h>
#include <ATen/ops/trapz_meta.h>
#include <ATen/ops/triangular_solve_meta.h>
#include <ATen/ops/tril_meta.h>
#include <ATen/ops/tril_indices_meta.h>
#include <ATen/ops/triplet_margin_loss_meta.h>
#include <ATen/ops/triu_meta.h>
#include <ATen/ops/triu_indices_meta.h>
#include <ATen/ops/true_divide_meta.h>
#include <ATen/ops/trunc_meta.h>
#include <ATen/ops/type_as_meta.h>
#include <ATen/ops/unbind_meta.h>
#include <ATen/ops/unbind_copy_meta.h>
#include <ATen/ops/unflatten_meta.h>
#include <ATen/ops/unflatten_dense_tensors_meta.h>
#include <ATen/ops/unfold_meta.h>
#include <ATen/ops/unfold_backward_meta.h>
#include <ATen/ops/unfold_copy_meta.h>
#include <ATen/ops/uniform_meta.h>
#include <ATen/ops/unique_consecutive_meta.h>
#include <ATen/ops/unique_dim_meta.h>
#include <ATen/ops/unique_dim_consecutive_meta.h>
#include <ATen/ops/unsafe_chunk_meta.h>
#include <ATen/ops/unsafe_split_meta.h>
#include <ATen/ops/unsafe_split_with_sizes_meta.h>
#include <ATen/ops/unsqueeze_meta.h>
#include <ATen/ops/unsqueeze_copy_meta.h>
#include <ATen/ops/upsample_bicubic2d_meta.h>
#include <ATen/ops/upsample_bicubic2d_backward_meta.h>
#include <ATen/ops/upsample_bilinear2d_meta.h>
#include <ATen/ops/upsample_bilinear2d_backward_meta.h>
#include <ATen/ops/upsample_linear1d_meta.h>
#include <ATen/ops/upsample_linear1d_backward_meta.h>
#include <ATen/ops/upsample_nearest1d_meta.h>
#include <ATen/ops/upsample_nearest1d_backward_meta.h>
#include <ATen/ops/upsample_nearest2d_meta.h>
#include <ATen/ops/upsample_nearest2d_backward_meta.h>
#include <ATen/ops/upsample_nearest3d_meta.h>
#include <ATen/ops/upsample_nearest3d_backward_meta.h>
#include <ATen/ops/upsample_trilinear3d_meta.h>
#include <ATen/ops/upsample_trilinear3d_backward_meta.h>
#include <ATen/ops/value_selecting_reduction_backward_meta.h>
#include <ATen/ops/values_meta.h>
#include <ATen/ops/values_copy_meta.h>
#include <ATen/ops/vander_meta.h>
#include <ATen/ops/var_meta.h>
#include <ATen/ops/var_mean_meta.h>
#include <ATen/ops/vdot_meta.h>
#include <ATen/ops/view_meta.h>
#include <ATen/ops/view_as_meta.h>
#include <ATen/ops/view_as_complex_meta.h>
#include <ATen/ops/view_as_complex_copy_meta.h>
#include <ATen/ops/view_as_real_meta.h>
#include <ATen/ops/view_as_real_copy_meta.h>
#include <ATen/ops/view_copy_meta.h>
#include <ATen/ops/vsplit_meta.h>
#include <ATen/ops/vstack_meta.h>
#include <ATen/ops/where_meta.h>
#include <ATen/ops/xlogy_meta.h>
#include <ATen/ops/xor_meta.h>
#include <ATen/ops/zero_meta.h>
#include <ATen/ops/zeros_meta.h>
#include <ATen/ops/zeros_like_meta.h>

namespace at {

namespace meta {



} // namespace meta
} // namespace at
