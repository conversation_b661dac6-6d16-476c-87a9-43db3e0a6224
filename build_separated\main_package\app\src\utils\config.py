"""
配置管理模块
"""
import os
import sys
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class ModelConfig:
    """模型配置

    Attributes:
        embedding_model: 嵌入模型名称或路径
        llm_model: 大语言模型名称或路径
        max_tokens: 最大生成token数量 (1-8192)
        temperature: 生成温度 (0.0-2.0)
        device: 运行设备 ("auto", "cpu", "cuda")
        cpu_optimization: CPU优化配置
    """
    embedding_model: str = "shibing624/text2vec-base-chinese"
    llm_model: str = "chatglm3-6b-int4"
    max_tokens: int = 2048
    temperature: float = 0.7
    device: str = "auto"  # auto, cpu, cuda
    cpu_optimization: dict = None

    def __post_init__(self):
        if self.cpu_optimization is None:
            self.cpu_optimization = {
                'num_threads': 4,
                'use_mkl': True,
                'torch_num_threads': 4
            }

    def __post_init__(self):
        """验证配置参数"""
        if not isinstance(self.max_tokens, int) or not (1 <= self.max_tokens <= 8192):
            raise ValueError(f"max_tokens必须是1-8192之间的整数，当前值: {self.max_tokens}")

        if not isinstance(self.temperature, (int, float)) or not (0.0 <= self.temperature <= 2.0):
            raise ValueError(f"temperature必须是0.0-2.0之间的数值，当前值: {self.temperature}")

        if self.device not in ["auto", "cpu", "cuda"]:
            raise ValueError(f"device必须是 'auto', 'cpu' 或 'cuda'，当前值: {self.device}")

        if not self.embedding_model.strip():
            raise ValueError("embedding_model不能为空")

        if not self.llm_model.strip():
            raise ValueError("llm_model不能为空")


@dataclass
class DatabaseConfig:
    """数据库配置"""
    chroma_persist_dir: str = "data/chroma_db"
    whoosh_index_dir: str = "data/whoosh_index"
    collection_name: str = "policy_documents"


@dataclass
class ProcessingConfig:
    """文档处理配置"""
    chunk_size: int = 512
    chunk_overlap: int = 50
    supported_formats: list = None
    ocr_language: str = "chi_sim+eng"
    
    def __post_init__(self):
        if self.supported_formats is None:
            self.supported_formats = ['.pdf', '.docx', '.doc', '.xlsx', '.xls']


@dataclass
class UIConfig:
    """界面配置"""
    window_title: str = "公司制度查询平台"
    window_width: int = 1200
    window_height: int = 800
    theme: str = "light"  # light, dark


@dataclass
class AppConfig:
    """应用配置"""
    model: ModelConfig
    database: DatabaseConfig
    processing: ProcessingConfig
    ui: UIConfig
    
    # 路径配置
    base_dir: str = ""
    docs_dir: str = "docs"
    data_dir: str = "data"
    models_dir: str = "models"
    preview_dir: str = "data/preview_pdfs"
    
    # 性能配置
    max_memory_gb: int = 8
    enable_cache: bool = True
    cache_size: int = 1000
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "logs/app.log"


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "config.yaml"

        # 获取程序根目录
        if getattr(sys, 'frozen', False):
            # 打包后的程序，获取可执行文件所在目录的上级目录
            self.base_dir = Path(sys.executable).parent.parent
        else:
            # 开发环境
            self.base_dir = Path(__file__).parent.parent.parent

        self._config: Optional[AppConfig] = None
    
    def load_config(self) -> AppConfig:
        """加载配置"""
        if self._config is not None:
            return self._config

        config_file = self.base_dir / self.config_path

        if config_file.exists():
            # 检查文件权限（仅警告，不阻止启动）
            self._check_file_permissions(config_file)

            with open(config_file, 'r', encoding='utf-8') as f:
                try:
                    config_dict = yaml.safe_load(f)
                    if not isinstance(config_dict, dict):
                        raise ValueError("配置文件格式错误：根节点必须是字典")
                except yaml.YAMLError as e:
                    raise ValueError(f"配置文件YAML格式错误: {e}")

            self._config = self._dict_to_config(config_dict)
        else:
            self._config = self._get_default_config()
            self.save_config()

        # 设置绝对路径
        self._config.base_dir = str(self.base_dir)
        self._update_paths()

        # 验证配置（延迟验证，避免循环依赖）
        try:
            self._validate_config()
        except Exception as e:
            from .exceptions import ConfigurationError
            logger = self._get_logger()
            logger.error(f"配置验证失败: {e}")
            # 对于关键配置错误，应该阻止程序启动
            if self._is_critical_config_error(e):
                raise ConfigurationError(f"关键配置验证失败: {e}")
            else:
                logger.warning("非关键配置错误，程序继续运行")

        return self._config
    
    def save_config(self):
        """保存配置"""
        if self._config is None:
            return
            
        config_dict = self._config_to_dict(self._config)
        config_file = self.base_dir / self.config_path
        
        # 确保目录存在
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config_dict, f, default_flow_style=False, 
                     allow_unicode=True, indent=2)
    
    def _get_default_config(self) -> AppConfig:
        """获取默认配置"""
        return AppConfig(
            model=ModelConfig(),
            database=DatabaseConfig(),
            processing=ProcessingConfig(),
            ui=UIConfig()
        )
    
    def _dict_to_config(self, config_dict: Dict[str, Any]) -> AppConfig:
        """字典转配置对象"""
        model_config = ModelConfig(**config_dict.get('model', {}))
        database_config = DatabaseConfig(**config_dict.get('database', {}))
        processing_config = ProcessingConfig(**config_dict.get('processing', {}))
        ui_config = UIConfig(**config_dict.get('ui', {}))
        
        app_config = AppConfig(
            model=model_config,
            database=database_config,
            processing=processing_config,
            ui=ui_config
        )
        
        # 更新其他配置
        for key, value in config_dict.items():
            if key not in ['model', 'database', 'processing', 'ui']:
                if hasattr(app_config, key):
                    setattr(app_config, key, value)
        
        return app_config
    
    def _config_to_dict(self, config: AppConfig) -> Dict[str, Any]:
        """配置对象转字典"""
        return {
            'model': {
                'embedding_model': config.model.embedding_model,
                'llm_model': config.model.llm_model,
                'max_tokens': config.model.max_tokens,
                'temperature': config.model.temperature,
                'device': config.model.device
            },
            'database': {
                'chroma_persist_dir': config.database.chroma_persist_dir,
                'whoosh_index_dir': config.database.whoosh_index_dir,
                'collection_name': config.database.collection_name
            },
            'processing': {
                'chunk_size': config.processing.chunk_size,
                'chunk_overlap': config.processing.chunk_overlap,
                'supported_formats': config.processing.supported_formats,
                'ocr_language': config.processing.ocr_language
            },
            'ui': {
                'window_title': config.ui.window_title,
                'window_width': config.ui.window_width,
                'window_height': config.ui.window_height,
                'theme': config.ui.theme
            },
            'docs_dir': config.docs_dir,
            'data_dir': config.data_dir,
            'models_dir': config.models_dir,
            'preview_dir': config.preview_dir,
            'max_memory_gb': config.max_memory_gb,
            'enable_cache': config.enable_cache,
            'cache_size': config.cache_size,
            'log_level': config.log_level,
            'log_file': config.log_file
        }
    
    def _update_paths(self):
        """更新路径为绝对路径"""
        base_path = Path(self._config.base_dir)
        
        # 更新数据库路径
        self._config.database.chroma_persist_dir = str(
            base_path / self._config.database.chroma_persist_dir
        )
        self._config.database.whoosh_index_dir = str(
            base_path / self._config.database.whoosh_index_dir
        )
        
        # 更新其他路径
        self._config.docs_dir = str(base_path / self._config.docs_dir)
        self._config.data_dir = str(base_path / self._config.data_dir)
        self._config.models_dir = str(base_path / self._config.models_dir)
        self._config.preview_dir = str(base_path / self._config.preview_dir)
        self._config.log_file = str(base_path / self._config.log_file)

    def _validate_config(self):
        """验证配置"""
        if self._config is None:
            raise ValueError("配置未加载")

        # 验证必要的配置项
        required_fields = {
            'model.embedding_model': self._config.model.embedding_model,
            'model.llm_model': self._config.model.llm_model,
            'database.collection_name': self._config.database.collection_name,
            'ui.window_title': self._config.ui.window_title
        }

        for field_name, value in required_fields.items():
            if not value or (isinstance(value, str) and not value.strip()):
                raise ValueError(f"必要配置项为空: {field_name}")

        # 验证数值范围
        if self._config.processing.chunk_size <= 0:
            raise ValueError("chunk_size必须大于0")

        if self._config.processing.chunk_overlap < 0:
            raise ValueError("chunk_overlap不能为负数")

        if self._config.processing.chunk_overlap >= self._config.processing.chunk_size:
            raise ValueError("chunk_overlap必须小于chunk_size")

        if self._config.model.max_tokens <= 0:
            raise ValueError("max_tokens必须大于0")

        if not (0 <= self._config.model.temperature <= 2):
            raise ValueError("temperature必须在0-2之间")

        if self._config.max_memory_gb <= 0:
            raise ValueError("max_memory_gb必须大于0")

        # 验证支持的文件格式
        valid_formats = {'.pdf', '.docx', '.doc', '.xlsx', '.xls', '.txt'}
        for fmt in self._config.processing.supported_formats:
            if fmt not in valid_formats:
                print(f"警告: 不支持的文件格式: {fmt}")

        logger = self._get_logger()
        logger.info("配置验证通过")

    def _check_file_permissions(self, file_path: Path) -> bool:
        """检查文件权限安全性"""
        try:
            import stat
            import platform

            # Windows系统的权限检查较为复杂，暂时跳过严格检查
            if platform.system() == 'Windows':
                print(f"信息: Windows系统跳过文件权限检查: {file_path}")
                return True

            file_stat = file_path.stat()

            # Unix/Linux系统的权限检查
            # 检查文件是否被其他用户可写
            if file_stat.st_mode & stat.S_IWOTH:
                print(f"警告: 配置文件对其他用户可写: {file_path}")
                print("建议: 运行 chmod 644 config.yaml 来修复权限")
                return False

            # 检查文件是否被组用户可写（可选，根据需求调整）
            if file_stat.st_mode & stat.S_IWGRP:
                print(f"警告: 配置文件对组用户可写: {file_path}")
                print("建议: 运行 chmod 644 config.yaml 来修复权限")
                # 这里可以选择返回False，根据安全需求决定

            return True
        except Exception as e:
            print(f"错误: 检查文件权限失败: {e}")
            return True  # 检查失败时允许继续，但记录错误

    def _get_logger(self):
        """获取logger实例"""
        try:
            from .logger import get_logger
            return get_logger(__name__)
        except Exception:
            # 如果logger不可用，返回一个简单的打印函数
            class SimpleLogger:
                def info(self, msg): print(f"INFO: {msg}")
                def warning(self, msg): print(f"WARNING: {msg}")
                def error(self, msg): print(f"ERROR: {msg}")
            return SimpleLogger()

    def _is_critical_config_error(self, error: Exception) -> bool:
        """判断是否为关键配置错误"""
        error_str = str(error).lower()
        critical_keywords = [
            'embedding_model不能为空',
            'llm_model不能为空',
            'collection_name不能为空',
            'window_title不能为空'
        ]

        return any(keyword in error_str for keyword in critical_keywords)


# 全局配置管理器实例
config_manager = ConfigManager()


def get_config() -> AppConfig:
    """获取应用配置"""
    return config_manager.load_config()


def save_config():
    """保存配置"""
    config_manager.save_config()
