<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>PDF Viewer</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .pdf-container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .pdf-toolbar {
            background-color: #333;
            color: white;
            padding: 4px 12px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            flex-shrink: 0;
            height: 32px;
            box-sizing: border-box;
        }
        
        .pdf-toolbar button {
            background-color: #555;
            color: white;
            border: none;
            padding: 3px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
            height: 24px;
            line-height: 1;
        }
        
        .pdf-toolbar button:hover {
            background-color: #666;
        }
        
        .pdf-toolbar button:disabled {
            background-color: #444;
            color: #888;
            cursor: not-allowed;
        }
        
        .pdf-toolbar input {
            width: 50px;
            padding: 2px 4px;
            border: 1px solid #666;
            border-radius: 3px;
            background-color: #555;
            color: white;
            text-align: center;
            font-size: 11px;
            height: 20px;
            box-sizing: border-box;
        }
        
        .pdf-viewer {
            flex: 1;
            overflow-y: auto;
            overflow-x: auto;
            background-color: #f5f5f5;
            padding: 5px;
            scroll-behavior: smooth;
        }

        .pdf-page-container {
            margin: 10px auto;
            text-align: center;
        }

        .pdf-page {
            margin: 5px auto;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            background-color: white;
            display: block;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .pdf-page-label {
            background-color: #333;
            color: white;
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 3px;
            margin-bottom: 5px;
            display: inline-block;
            font-family: Arial, sans-serif;
        }
        
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            color: #666;
            font-size: 16px;
        }
        
        .error {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            color: #d32f2f;
            font-size: 16px;
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="pdf-container">
        <div class="pdf-toolbar">
            <button id="prevPage">◀</button>
            <span style="font-size: 11px;">
                <input type="number" id="pageNum" value="1" min="1"> /
                <span id="pageCount">-</span>
            </span>
            <button id="nextPage">▶</button>
            <span style="margin-left: 10px; font-size: 11px;">缩放:</span>
            <button id="zoomOut">-</button>
            <span id="zoomLevel" style="font-size: 11px; min-width: 35px; text-align: center;">100%</span>
            <button id="zoomIn">+</button>
            <button id="fitWidth">适宽</button>
            <button id="fitPage">适页</button>
        </div>
        <div class="pdf-viewer" id="pdfViewer">
            <div class="loading" id="loading">正在加载PDF...</div>
        </div>
    </div>

    <script type="module">
        import * as pdfjsLib from './build/pdf.mjs';
        
        // 设置PDF.js worker
        pdfjsLib.GlobalWorkerOptions.workerSrc = './build/pdf.worker.mjs';
        
        class PDFViewer {
            constructor() {
                this.pdfDoc = null;
                this.pageNum = 1;
                this.pageCount = 0;
                this.scale = 1.0;
                this.canvas = null;
                this.ctx = null;
                
                this.initElements();
                this.bindEvents();
            }
            
            initElements() {
                this.viewer = document.getElementById('pdfViewer');
                this.loading = document.getElementById('loading');
                this.pageNumInput = document.getElementById('pageNum');
                this.pageCountSpan = document.getElementById('pageCount');
                this.zoomLevelSpan = document.getElementById('zoomLevel');
                
                this.prevBtn = document.getElementById('prevPage');
                this.nextBtn = document.getElementById('nextPage');
                this.zoomInBtn = document.getElementById('zoomIn');
                this.zoomOutBtn = document.getElementById('zoomOut');
                this.fitWidthBtn = document.getElementById('fitWidth');
                this.fitPageBtn = document.getElementById('fitPage');
            }
            
            bindEvents() {
                this.prevBtn.addEventListener('click', () => this.prevPage());
                this.nextBtn.addEventListener('click', () => this.nextPage());
                this.zoomInBtn.addEventListener('click', () => this.zoomIn());
                this.zoomOutBtn.addEventListener('click', () => this.zoomOut());
                this.fitWidthBtn.addEventListener('click', () => this.fitWidth());
                this.fitPageBtn.addEventListener('click', () => this.fitPage());
                
                this.pageNumInput.addEventListener('change', (e) => {
                    const pageNum = parseInt(e.target.value);
                    if (pageNum >= 1 && pageNum <= this.pageCount) {
                        this.goToPage(pageNum);
                    } else {
                        e.target.value = this.pageNum;
                    }
                });
                
                // 监听来自父窗口的消息
                window.addEventListener('message', (event) => {
                    if (event.data.type === 'loadPDF') {
                        this.loadPDF(event.data.url, event.data.page || 1);
                    }
                });
            }
            
            async loadPDF(url, initialPage = 1) {
                try {
                    this.showLoading();
                    
                    const loadingTask = pdfjsLib.getDocument(url);
                    this.pdfDoc = await loadingTask.promise;
                    this.pageCount = this.pdfDoc.numPages;
                    
                    this.pageCountSpan.textContent = this.pageCount;
                    this.pageNum = Math.min(initialPage, this.pageCount);
                    this.pageNumInput.value = this.pageNum;
                    
                    // 首次加载时渲染所有页面并自动适合宽度
                    await this.renderAllPages();
                    this.fitWidth();
                    this.updateButtons();

                    // 如果指定了初始页面，滚动到该页面
                    if (initialPage > 1) {
                        console.log(`准备跳转到初始页面: ${initialPage}`);
                        setTimeout(() => {
                            console.log(`执行初始页面跳转: ${initialPage}`);
                            this.goToPage(initialPage);
                        }, 500); // 增加延迟以确保所有页面都已渲染完成
                    }
                    
                } catch (error) {
                    console.error('PDF加载失败:', error);
                    this.showError('PDF加载失败: ' + error.message);
                }
            }
            
            async renderAllPages() {
                if (!this.pdfDoc) return;

                try {
                    // 清空viewer
                    this.viewer.innerHTML = '';

                    // 渲染所有页面
                    for (let pageNum = 1; pageNum <= this.pageCount; pageNum++) {
                        await this.renderSinglePage(pageNum);
                    }

                } catch (error) {
                    console.error('页面渲染失败:', error);
                    this.showError('页面渲染失败: ' + error.message);
                }
            }

            async renderSinglePage(pageNum) {
                try {
                    const page = await this.pdfDoc.getPage(pageNum);
                    const viewport = page.getViewport({ scale: this.scale });

                    // 创建页面容器
                    const pageContainer = document.createElement('div');
                    pageContainer.className = 'pdf-page-container';
                    pageContainer.style.marginBottom = '10px';
                    pageContainer.style.textAlign = 'center';

                    // 创建页码标签
                    const pageLabel = document.createElement('div');
                    pageLabel.className = 'pdf-page-label';
                    pageLabel.textContent = `第 ${pageNum} 页`;
                    pageLabel.style.cssText = `
                        background-color: #333;
                        color: white;
                        padding: 4px 8px;
                        font-size: 12px;
                        border-radius: 3px;
                        margin-bottom: 5px;
                        display: inline-block;
                    `;

                    // 创建canvas
                    const canvas = document.createElement('canvas');
                    canvas.className = 'pdf-page';
                    canvas.width = viewport.width;
                    canvas.height = viewport.height;

                    const ctx = canvas.getContext('2d');

                    // 渲染页面
                    const renderContext = {
                        canvasContext: ctx,
                        viewport: viewport
                    };

                    await page.render(renderContext).promise;

                    // 添加到容器
                    pageContainer.appendChild(pageLabel);
                    pageContainer.appendChild(canvas);
                    this.viewer.appendChild(pageContainer);

                } catch (error) {
                    console.error(`第${pageNum}页渲染失败:`, error);
                }
            }
            
            showLoading() {
                this.viewer.innerHTML = '<div class="loading">正在加载PDF...</div>';
            }
            
            showError(message) {
                this.viewer.innerHTML = `<div class="error">${message}</div>`;
            }
            
            prevPage() {
                // 连续滚动模式：滚动到上一页
                const pageContainers = this.viewer.querySelectorAll('.pdf-page-container');
                if (this.pageNum > 1 && pageContainers[this.pageNum - 2]) {
                    this.pageNum--;
                    this.pageNumInput.value = this.pageNum;
                    pageContainers[this.pageNum - 1].scrollIntoView({ behavior: 'smooth', block: 'start' });
                    this.updateButtons();
                }
            }

            nextPage() {
                // 连续滚动模式：滚动到下一页
                const pageContainers = this.viewer.querySelectorAll('.pdf-page-container');
                if (this.pageNum < this.pageCount && pageContainers[this.pageNum]) {
                    this.pageNum++;
                    this.pageNumInput.value = this.pageNum;
                    pageContainers[this.pageNum - 1].scrollIntoView({ behavior: 'smooth', block: 'start' });
                    this.updateButtons();
                }
            }

            goToPage(pageNum) {
                // 连续滚动模式：滚动到指定页
                console.log(`跳转到页面: ${pageNum}`);
                const pageContainers = this.viewer.querySelectorAll('.pdf-page-container');
                console.log(`找到页面容器数量: ${pageContainers.length}`);

                if (pageNum >= 1 && pageNum <= this.pageCount && pageContainers[pageNum - 1]) {
                    this.pageNum = pageNum;
                    this.pageNumInput.value = this.pageNum;

                    // 滚动到指定页面
                    const targetContainer = pageContainers[pageNum - 1];
                    console.log(`滚动到页面容器:`, targetContainer);

                    // 使用立即滚动，确保在缩放后能准确定位
                    targetContainer.scrollIntoView({ behavior: 'auto', block: 'start' });

                    // 短暂高亮显示目标页面
                    targetContainer.style.border = '2px solid #007acc';
                    setTimeout(() => {
                        targetContainer.style.border = '';
                    }, 1000);

                    this.updateButtons();
                } else {
                    console.log(`页面跳转失败: pageNum=${pageNum}, pageCount=${this.pageCount}, containers=${pageContainers.length}`);
                }
            }
            
            async zoomIn() {
                this.scale = Math.min(this.scale * 1.2, 3.0);
                await this.updateZoom();
            }

            async zoomOut() {
                this.scale = Math.max(this.scale / 1.2, 0.3);
                await this.updateZoom();
            }
            
            async fitWidth() {
                if (this.pdfDoc) {
                    try {
                        const page = await this.pdfDoc.getPage(this.pageNum);
                        const viewport = page.getViewport({ scale: 1.0 });
                        const containerWidth = this.viewer.clientWidth - 20; // 减去边距
                        this.scale = containerWidth / viewport.width;
                        await this.updateZoom();
                    } catch (error) {
                        console.error('适合宽度失败:', error);
                    }
                }
            }
            
            async fitPage() {
                if (this.pdfDoc) {
                    try {
                        const page = await this.pdfDoc.getPage(this.pageNum);
                        const viewport = page.getViewport({ scale: 1.0 });
                        const containerWidth = this.viewer.clientWidth - 20;
                        const containerHeight = this.viewer.clientHeight - 20;
                        const scaleX = containerWidth / viewport.width;
                        const scaleY = containerHeight / viewport.height;
                        this.scale = Math.min(scaleX, scaleY);
                        await this.updateZoom();
                    } catch (error) {
                        console.error('适合页面失败:', error);
                    }
                }
            }
            
            async updateZoom() {
                this.zoomLevelSpan.textContent = Math.round(this.scale * 100) + '%';

                // 记住当前页面
                const currentPage = this.pageNum;
                console.log(`缩放前当前页面: ${currentPage}`);

                // 重新渲染所有页面
                await this.renderAllPages();

                // 缩放后滚动回当前页面
                setTimeout(() => {
                    console.log(`缩放后滚动到页面: ${currentPage}`);
                    this.goToPage(currentPage);
                }, 200); // 延迟确保渲染完成
            }
            
            updateButtons() {
                this.prevBtn.disabled = this.pageNum <= 1;
                this.nextBtn.disabled = this.pageNum >= this.pageCount;
            }
        }
        
        // 初始化PDF查看器
        const viewer = new PDFViewer();
        
        // 暴露给父窗口
        window.pdfViewer = viewer;
        
        // 通知父窗口已准备就绪
        if (window.parent !== window) {
            window.parent.postMessage({ type: 'pdfViewerReady' }, '*');
        }
    </script>
</body>
</html>
