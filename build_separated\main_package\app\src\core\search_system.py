"""
搜索系统模块
整合向量搜索、全文搜索和AI问答功能
"""
import os
import time
import math
import threading
import hashlib
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
from collections import OrderedDict
import functools
from concurrent.futures import ThreadPoolExecutor, as_completed

from .vector_store import VectorStore
from .text_index import TextIndex
from .ai_model import get_ai_model
from .session_manager import get_session_manager
from ..utils.config import get_config
from ..utils.logger import get_logger
from ..utils.helpers import truncate_text, clean_text

logger = get_logger(__name__)


def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
    """重试装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        logger.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {e}，{delay}秒后重试")
                        time.sleep(delay * (2 ** attempt))  # 指数退避
                    else:
                        logger.error(f"函数 {func.__name__} 重试 {max_retries} 次后仍然失败")
            raise last_exception
        return wrapper
    return decorator


@dataclass
class SearchResult:
    """搜索结果数据结构"""
    id: str
    content: str
    title: str
    source_file: str
    file_name: str
    department: str
    page_number: Optional[int]
    score: float
    search_type: str  # 'vector', 'text', 'hybrid'
    metadata: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'content': self.content,
            'title': self.title,
            'source_file': self.source_file,
            'file_name': self.file_name,
            'department': self.department,
            'page_number': self.page_number,
            'score': self.score,
            'search_type': self.search_type,
            'metadata': self.metadata
        }


@dataclass
class AIResponse:
    """AI回答数据结构"""
    answer: str
    sources: List[SearchResult]
    query: str
    response_time: float
    confidence: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'answer': self.answer,
            'sources': [source.to_dict() for source in self.sources],
            'query': self.query,
            'response_time': self.response_time,
            'confidence': self.confidence
        }


class ThreadSafeCache:
    """线程安全的LRU缓存"""

    def __init__(self, max_size: int = 1000):
        self._cache = OrderedDict()
        self._max_size = max_size
        self._lock = threading.RLock()

    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            if key in self._cache:
                # 移动到末尾（最近使用）
                value = self._cache.pop(key)
                self._cache[key] = value
                return value
            return None

    def set(self, key: str, value: Any):
        """设置缓存值"""
        with self._lock:
            # 如果已存在，先删除
            if key in self._cache:
                del self._cache[key]

            # 限制缓存大小
            while len(self._cache) >= self._max_size:
                # 删除最旧的条目（第一个）
                self._cache.popitem(last=False)

            # 添加到末尾
            self._cache[key] = value

    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()

    def size(self) -> int:
        """获取缓存大小"""
        with self._lock:
            return len(self._cache)


class PolicySearchSystem:
    """公司制度搜索系统"""

    def __init__(self):
        self.config = get_config()

        # 初始化组件
        self.vector_store = None
        self.text_index = None
        self.ai_model = None

        # 线程安全缓存
        self.search_cache = ThreadSafeCache(self.config.cache_size)
        self.cache_enabled = self.config.enable_cache

        # 性能优化配置
        self.max_workers = min(4, (os.cpu_count() or 1) + 1)  # 线程池大小
        self.batch_size = 10  # 批处理大小

        # 初始化
        self._initialize_components()
    
    def _initialize_components(self):
        """初始化组件"""
        try:
            logger.info("初始化搜索系统组件...")
            
            # 初始化向量存储
            self.vector_store = VectorStore()
            logger.info("向量存储初始化完成")
            
            # 初始化全文索引
            self.text_index = TextIndex()
            logger.info("全文索引初始化完成")
            
            # 获取AI模型实例（惰性加载）
            self.ai_model = get_ai_model()
            logger.info("AI模型管理器初始化完成")
            
            logger.info("搜索系统初始化完成")
            
        except Exception as e:
            logger.error(f"搜索系统初始化失败: {e}")
            raise
    
    def _get_cache_key(self, query: str, search_type: str, **kwargs) -> str:
        """生成缓存键（优化版本）"""
        # 使用更高效的键生成方式
        key_parts = [query, search_type]
        if kwargs:
            # 只包含有值的参数
            filtered_kwargs = {k: v for k, v in kwargs.items() if v is not None}
            if filtered_kwargs:
                key_parts.append(str(sorted(filtered_kwargs.items())))

        key_data = "_".join(key_parts)
        # 使用更快的哈希算法
        return hashlib.blake2b(key_data.encode(), digest_size=16).hexdigest()

    def _get_from_cache(self, cache_key: str) -> Optional[Any]:
        """从缓存获取结果"""
        if not self.cache_enabled:
            return None
        return self.search_cache.get(cache_key)

    def _save_to_cache(self, cache_key: str, result: Any):
        """保存到缓存"""
        if not self.cache_enabled:
            return
        self.search_cache.set(cache_key, result)
    
    def vector_search(self, query: str, top_k: int = 5) -> List[SearchResult]:
        """向量语义搜索"""
        try:
            # 检查缓存
            cache_key = self._get_cache_key(query, "vector", top_k=top_k)
            cached_result = self._get_from_cache(cache_key)
            if cached_result is not None:
                logger.info(f"从缓存返回向量搜索结果: {query}")
                return cached_result
            
            # 执行向量搜索
            vector_results = self.vector_store.search(query, top_k)
            
            # 转换为SearchResult格式
            search_results = []
            for result in vector_results:
                metadata = result['metadata']
                
                search_result = SearchResult(
                    id=metadata.get('chunk_id', ''),
                    content=result['content'],
                    title=truncate_text(result['content'], 100),
                    source_file=metadata.get('file_path', ''),
                    file_name=metadata.get('file_name', ''),
                    department=metadata.get('department', ''),
                    page_number=metadata.get('page_number'),
                    score=result['score'],
                    search_type='vector',
                    metadata=metadata
                )
                search_results.append(search_result)
            
            # 保存到缓存
            self._save_to_cache(cache_key, search_results)
            
            logger.info(f"向量搜索完成: {query}, 返回 {len(search_results)} 个结果")
            return search_results
            
        except Exception as e:
            logger.error(f"向量搜索失败: {e}")
            return []
    
    def text_search(self, query: str, top_k: int = 10,
                   department: Optional[str] = None,
                   document_type: Optional[str] = None) -> List[SearchResult]:
        """全文关键词搜索"""
        try:
            # 检查缓存
            cache_key = self._get_cache_key(
                query, "text", 
                top_k=top_k, 
                department=department, 
                document_type=document_type
            )
            cached_result = self._get_from_cache(cache_key)
            if cached_result is not None:
                logger.info(f"从缓存返回全文搜索结果: {query}")
                return cached_result
            
            # 执行全文搜索
            text_results = self.text_index.search(
                query, top_k, department, document_type
            )
            
            # 转换为SearchResult格式
            search_results = []
            for result in text_results:
                search_result = SearchResult(
                    id=result['id'],
                    content=result['content'],
                    title=result['title'],
                    source_file=result['file_path'],
                    file_name=result['file_name'],
                    department=result['department'],
                    page_number=result['page_number'],
                    score=result['score'],
                    search_type='text',
                    metadata={
                        'chunk_index': result['chunk_index'],
                        'document_type': result['document_type'],
                        'rank': result['rank']
                    }
                )
                search_results.append(search_result)
            
            # 保存到缓存
            self._save_to_cache(cache_key, search_results)
            
            logger.info(f"全文搜索完成: {query}, 返回 {len(search_results)} 个结果")
            return search_results
            
        except Exception as e:
            logger.error(f"全文搜索失败: {e}")
            return []
    
    def hybrid_search(self, query: str, top_k: int = 10) -> List[SearchResult]:
        """混合搜索（向量+全文）"""
        try:
            # 检查缓存
            cache_key = self._get_cache_key(query, "hybrid", top_k=top_k)
            cached_result = self._get_from_cache(cache_key)
            if cached_result is not None:
                logger.info(f"从缓存返回混合搜索结果: {query}")
                return cached_result
            
            # 确保每个搜索至少返回1个结果
            vector_k = max(1, top_k // 2)
            text_k = max(1, top_k // 2)

            # 执行向量搜索和全文搜索
            vector_results = self.vector_search(query, vector_k)
            text_results = self.text_search(query, text_k)
            
            # 合并结果并去重
            all_results = {}
            
            # 添加向量搜索结果
            for result in vector_results:
                result.search_type = 'hybrid'
                all_results[result.id] = result
            
            # 添加全文搜索结果
            for result in text_results:
                if result.id in all_results:
                    # 如果已存在，取较高的分数
                    existing = all_results[result.id]
                    if result.score > existing.score:
                        result.search_type = 'hybrid'
                        all_results[result.id] = result
                else:
                    result.search_type = 'hybrid'
                    all_results[result.id] = result
            
            # 按分数排序
            search_results = sorted(
                all_results.values(), 
                key=lambda x: x.score, 
                reverse=True
            )[:top_k]
            
            # 保存到缓存
            self._save_to_cache(cache_key, search_results)
            
            logger.info(f"混合搜索完成: {query}, 返回 {len(search_results)} 个结果")
            return search_results
            
        except Exception as e:
            logger.error(f"混合搜索失败: {e}")
            return []
    
    def ai_question(self, query: str, top_k: int = 5) -> AIResponse:
        """AI智能问答"""
        start_time = time.time()
        
        try:
            logger.info(f"开始AI问答: {query}")
            
            # 检查缓存
            cache_key = self._get_cache_key(query, "ai_question", top_k=top_k)
            cached_result = self._get_from_cache(cache_key)
            if cached_result is not None:
                logger.info(f"从缓存返回AI问答结果: {query}")
                return cached_result
            
            # 1. 向量检索获取相关上下文
            search_results = self.vector_search(query, top_k)

            if not search_results:
                # 没有找到相关文档，进行通用AI问答
                logger.info(f"未找到相关文档，进行通用AI问答: {query}")
                try:
                    # 直接调用AI模型进行通用问答
                    ai_answer = self.ai_model.generate_response(query)
                    response = AIResponse(
                        answer=ai_answer,
                        sources=[],
                        query=query,
                        response_time=time.time() - start_time,
                        confidence=0.5  # 通用问答置信度较低
                    )
                    # 缓存结果
                    self._save_to_cache(cache_key, response)
                    return response
                except Exception as e:
                    logger.error(f"通用AI问答失败: {e}")
                    response = AIResponse(
                        answer="抱歉，AI服务暂时不可用，请稍后再试。",
                        sources=[],
                        query=query,
                        response_time=time.time() - start_time
                    )
                    return response
            
            # 2. 准备上下文
            context_texts = []
            for i, result in enumerate(search_results):
                context = f"文档：{result.file_name}\n内容：{result.content}"
                context_texts.append(context)
            
            # 3. 生成AI回答（单轮对话，无历史上下文）
            answer = self.ai_model.chat(query, context_texts)
            
            # 5. 构建响应
            response_time = time.time() - start_time
            
            response = AIResponse(
                answer=answer,
                sources=search_results,
                query=query,
                response_time=response_time,
                confidence=self._calculate_confidence(search_results)
            )
            
            # 保存到缓存
            self._save_to_cache(cache_key, response)
            
            logger.info(f"AI问答完成: {query}, 耗时 {response_time:.2f}秒")
            return response
            
        except Exception as e:
            logger.error(f"AI问答失败: {e}")
            response = AIResponse(
                answer=f"AI问答处理出错: {str(e)}",
                sources=[],
                query=query,
                response_time=time.time() - start_time
            )
            return response
    
    def _calculate_confidence(self, search_results: List[SearchResult]) -> float:
        """计算回答置信度"""
        if not search_results:
            return 0.0
        
        # 基于搜索结果的平均分数计算置信度
        avg_score = sum(result.score for result in search_results) / len(search_results)
        
        # 归一化到0-1范围
        confidence = min(avg_score, 1.0)
        
        return confidence
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        try:
            vector_stats = self.vector_store.get_collection_stats()
            index_stats = self.text_index.get_index_stats()
            model_info = self.ai_model.get_model_info()
            
            return {
                'vector_store': vector_stats,
                'text_index': index_stats,
                'ai_model': model_info,
                'cache_size': self.search_cache.size(),
                'cache_enabled': self.cache_enabled
            }
            
        except Exception as e:
            logger.error(f"获取系统统计失败: {e}")
            return {}
    
    def batch_search(self, queries: List[str], search_type: str = "hybrid",
                    top_k: int = 5) -> Dict[str, List[SearchResult]]:
        """批量搜索"""
        if not queries:
            return {}

        results = {}

        # 检查缓存
        cached_results = {}
        uncached_queries = []

        for query in queries:
            cache_key = self._get_cache_key(query, search_type, top_k=top_k)
            cached_result = self._get_from_cache(cache_key)
            if cached_result is not None:
                cached_results[query] = cached_result
            else:
                uncached_queries.append(query)

        # 并行处理未缓存的查询
        if uncached_queries:
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交任务
                future_to_query = {}
                for query in uncached_queries:
                    if search_type == "vector":
                        future = executor.submit(self._vector_search_internal, query, top_k)
                    elif search_type == "text":
                        future = executor.submit(self._text_search_internal, query, top_k)
                    else:  # hybrid
                        future = executor.submit(self._hybrid_search_internal, query, top_k)
                    future_to_query[future] = query

                # 收集结果
                for future in as_completed(future_to_query):
                    query = future_to_query[future]
                    try:
                        result = future.result()
                        results[query] = result

                        # 保存到缓存
                        cache_key = self._get_cache_key(query, search_type, top_k=top_k)
                        self._save_to_cache(cache_key, result)

                    except Exception as e:
                        logger.error(f"批量搜索失败 {query}: {e}")
                        results[query] = []

        # 合并缓存结果
        results.update(cached_results)

        logger.info(f"批量搜索完成: {len(queries)} 个查询, {len(cached_results)} 个来自缓存")
        return results

    def _vector_search_internal(self, query: str, top_k: int) -> List[SearchResult]:
        """内部向量搜索方法（用于并行处理）"""
        try:
            vector_results = self.vector_store.search(query, top_k)
            return self._convert_to_search_results(vector_results, 'vector')
        except Exception as e:
            logger.error(f"向量搜索失败: {e}")
            return []

    def _text_search_internal(self, query: str, top_k: int) -> List[SearchResult]:
        """内部全文搜索方法（用于并行处理）"""
        try:
            text_results = self.text_index.search(query, top_k)
            return self._convert_to_search_results(text_results, 'text')
        except Exception as e:
            logger.error(f"全文搜索失败: {e}")
            return []

    def _hybrid_search_internal(self, query: str, top_k: int) -> List[SearchResult]:
        """内部混合搜索方法（用于并行处理）"""
        try:
            # 确保每个搜索至少返回1个结果
            vector_k = max(1, top_k // 2)
            text_k = max(1, top_k // 2)

            # 并行执行向量搜索和全文搜索
            with ThreadPoolExecutor(max_workers=2) as executor:
                vector_future = executor.submit(self._vector_search_internal, query, vector_k)
                text_future = executor.submit(self._text_search_internal, query, text_k)

                vector_results = vector_future.result()
                text_results = text_future.result()

            # 合并结果并去重
            all_results = {}

            for result in vector_results:
                result.search_type = 'hybrid'
                all_results[result.id] = result

            for result in text_results:
                if result.id in all_results:
                    existing = all_results[result.id]
                    if result.score > existing.score:
                        result.search_type = 'hybrid'
                        all_results[result.id] = result
                else:
                    result.search_type = 'hybrid'
                    all_results[result.id] = result

            # 按分数排序
            return sorted(all_results.values(), key=lambda x: x.score, reverse=True)[:top_k]

        except Exception as e:
            logger.error(f"混合搜索失败: {e}")
            return []

    def _convert_to_search_results(self, raw_results: List[Dict], search_type: str) -> List[SearchResult]:
        """转换原始结果为SearchResult格式"""
        search_results = []

        for result in raw_results:
            if search_type == 'vector':
                metadata = result['metadata']
                search_result = SearchResult(
                    id=metadata.get('chunk_id', ''),
                    content=result['content'],
                    title=truncate_text(result['content'], 100),
                    source_file=metadata.get('file_path', ''),
                    file_name=metadata.get('file_name', ''),
                    department=metadata.get('department', ''),
                    page_number=metadata.get('page_number'),
                    score=result['score'],
                    search_type=search_type,
                    metadata=metadata
                )
            else:  # text
                search_result = SearchResult(
                    id=result['id'],
                    content=result['content'],
                    title=result['title'],
                    source_file=result['file_path'],
                    file_name=result['file_name'],
                    department=result['department'],
                    page_number=result['page_number'],
                    score=result['score'],
                    search_type=search_type,
                    metadata={
                        'chunk_index': result['chunk_index'],
                        'document_type': result['document_type'],
                        'rank': result['rank']
                    }
                )
            search_results.append(search_result)

        return search_results

    def clear_cache(self):
        """清空缓存"""
        self.search_cache.clear()
        logger.info("搜索缓存已清空")
    
    def get_departments(self) -> List[str]:
        """获取所有部门列表"""
        try:
            return self.text_index.get_departments()
        except Exception as e:
            logger.error(f"获取部门列表失败: {e}")
            return []
    
    def get_document_types(self) -> List[str]:
        """获取所有文档类型列表"""
        try:
            return self.text_index.get_document_types()
        except Exception as e:
            logger.error(f"获取文档类型列表失败: {e}")
            return []

    def search_documents(self, query: str, limit: int = 10, offset: int = 0) -> Dict[str, Any]:
        """搜索文档（用于UI界面）"""
        try:
            # 执行混合搜索
            results = self.hybrid_search(query, limit + offset)

            # 分页处理
            paginated_results = results[offset:offset + limit]

            return {
                'results': paginated_results,
                'total': len(results),
                'page_size': limit,
                'current_page': (offset // limit) + 1,
                'total_pages': math.ceil(len(results) / limit) if limit > 0 else 1
            }

        except Exception as e:
            logger.error(f"文档搜索失败: {e}")
            return {
                'results': [],
                'total': 0,
                'page_size': limit,
                'current_page': 1,
                'total_pages': 1
            }


# 全局搜索系统实例
_search_system_instance = None


def get_search_system() -> PolicySearchSystem:
    """获取搜索系统实例（单例模式）"""
    global _search_system_instance
    if _search_system_instance is None:
        _search_system_instance = PolicySearchSystem()
    return _search_system_instance
