# LocalQA 公司制度查询平台 NSIS 安装脚本

# 定义常量
!define PRODUCT_NAME "LocalQA公司制度查询平台"
!define PRODUCT_VERSION "1.0.0"
!define PRODUCT_PUBLISHER "您的公司名称"
!define PRODUCT_WEB_SITE "http://www.yourcompany.com"
!define PRODUCT_DIR_REGKEY "Software\Microsoft\Windows\CurrentVersion\App Paths\LocalQA.exe"
!define PRODUCT_UNINST_KEY "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}"
!define PRODUCT_UNINST_ROOT_KEY "HKLM"

# 设置压缩方式
SetCompressor lzma

# 现代UI
!include "MUI2.nsh"

# 安装程序信息
Name "${PRODUCT_NAME} ${PRODUCT_VERSION}"
OutFile "LocalQA_Setup_${PRODUCT_VERSION}.exe"
InstallDir "$PROGRAMFILES64\LocalQA"
InstallDirRegKey HKLM "${PRODUCT_DIR_REGKEY}" ""
ShowInstDetails show
ShowUnInstDetails show

# 现代UI配置
!define MUI_ABORTWARNING
!define MUI_ICON "${NSISDIR}\Contrib\Graphics\Icons\modern-install.ico"
!define MUI_UNICON "${NSISDIR}\Contrib\Graphics\Icons\modern-uninstall.ico"

# 安装页面
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"
!insertmacro MUI_PAGE_COMPONENTS
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!define MUI_FINISHPAGE_RUN "$INSTDIR\LocalQA.bat"
!insertmacro MUI_PAGE_FINISH

# 卸载页面
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES

# 语言文件
!insertmacro MUI_LANGUAGE "SimpChinese"

# 安装程序版本信息
VIProductVersion "*******"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "ProductName" "${PRODUCT_NAME}"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "Comments" "AI驱动的公司制度查询平台"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "CompanyName" "${PRODUCT_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "LegalTrademarks" "Test Application is a trademark of ${PRODUCT_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "FileDescription" "${PRODUCT_NAME}"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "FileVersion" "${PRODUCT_VERSION}"

# 安装组件
Section "主程序" SEC01
  SetOutPath "$INSTDIR"
  SetOverwrite ifnewer
  
  # 复制Python环境
  File /r "build_portable\python"
  
  # 复制应用程序
  File /r "build_portable\app"
  
  # 复制启动脚本
  File "build_portable\LocalQA.bat"
  File "build_portable\start_localqa.py"
  
  # 创建桌面快捷方式
  CreateShortCut "$DESKTOP\LocalQA公司制度查询.lnk" "$INSTDIR\LocalQA.bat" "" "$INSTDIR\app\static\icon.ico"
  
  # 创建开始菜单快捷方式
  CreateDirectory "$SMPROGRAMS\LocalQA"
  CreateShortCut "$SMPROGRAMS\LocalQA\LocalQA公司制度查询.lnk" "$INSTDIR\LocalQA.bat" "" "$INSTDIR\app\static\icon.ico"
  CreateShortCut "$SMPROGRAMS\LocalQA\卸载LocalQA.lnk" "$INSTDIR\uninst.exe"
SectionEnd

Section "Visual C++ 运行库" SEC02
  # 检查并安装VC++ Redistributable
  SetOutPath "$TEMP"
  File "vcredist_x64.exe"
  ExecWait "$TEMP\vcredist_x64.exe /quiet /norestart"
  Delete "$TEMP\vcredist_x64.exe"
SectionEnd

Section -AdditionalIcons
  WriteIniStr "$INSTDIR\${PRODUCT_NAME}.url" "InternetShortcut" "URL" "${PRODUCT_WEB_SITE}"
  CreateShortCut "$SMPROGRAMS\LocalQA\网站.lnk" "$INSTDIR\${PRODUCT_NAME}.url"
SectionEnd

Section -Post
  WriteUninstaller "$INSTDIR\uninst.exe"
  WriteRegStr HKLM "${PRODUCT_DIR_REGKEY}" "" "$INSTDIR\LocalQA.bat"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayName" "$(^Name)"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "UninstallString" "$INSTDIR\uninst.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayIcon" "$INSTDIR\LocalQA.bat"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayVersion" "${PRODUCT_VERSION}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "URLInfoAbout" "${PRODUCT_WEB_SITE}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "Publisher" "${PRODUCT_PUBLISHER}"
SectionEnd

# 组件描述
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC01} "LocalQA主程序，包含完整的Python环境和AI模型"
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC02} "Microsoft Visual C++ 运行库，某些组件需要此运行库"
!insertmacro MUI_FUNCTION_DESCRIPTION_END

# 卸载程序
Section Uninstall
  Delete "$INSTDIR\${PRODUCT_NAME}.url"
  Delete "$INSTDIR\uninst.exe"
  Delete "$INSTDIR\LocalQA.bat"
  Delete "$INSTDIR\start_localqa.py"
  
  RMDir /r "$INSTDIR\python"
  RMDir /r "$INSTDIR\app"
  RMDir "$INSTDIR"

  Delete "$SMPROGRAMS\LocalQA\卸载LocalQA.lnk"
  Delete "$SMPROGRAMS\LocalQA\网站.lnk"
  Delete "$SMPROGRAMS\LocalQA\LocalQA公司制度查询.lnk"
  RMDir "$SMPROGRAMS\LocalQA"
  Delete "$DESKTOP\LocalQA公司制度查询.lnk"

  DeleteRegKey ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}"
  DeleteRegKey HKLM "${PRODUCT_DIR_REGKEY}"
  SetAutoClose true
SectionEnd
