# LocalQA Company Policy Query Platform NSIS Installation Script

# Define constants
!define PRODUCT_NAME "LocalQA"
!define PRODUCT_VERSION "1.0.0"
!define PRODUCT_PUBLISHER "Your Company Name"
!define PRODUCT_WEB_SITE "http://www.yourcompany.com"
!define PRODUCT_DIR_REGKEY "Software\Microsoft\Windows\CurrentVersion\App Paths\LocalQA.exe"
!define PRODUCT_UNINST_KEY "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}"
!define PRODUCT_UNINST_ROOT_KEY "HKLM"

# Set compression method
SetCompressor lzma

# Modern UI
!include "MUI2.nsh"

# Installer information
Name "${PRODUCT_NAME} ${PRODUCT_VERSION}"
OutFile "LocalQA_Setup_${PRODUCT_VERSION}.exe"
InstallDir "$PROGRAMFILES64\LocalQA"
InstallDirRegKey HKLM "${PRODUCT_DIR_REGKEY}" ""
ShowInstDetails show
ShowUnInstDetails show

# Modern UI configuration
!define MUI_ABORTWARNING
!define MUI_ICON "${NSISDIR}\Contrib\Graphics\Icons\modern-install.ico"
!define MUI_UNICON "${NSISDIR}\Contrib\Graphics\Icons\modern-uninstall.ico"

# Installation pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"
!insertmacro MUI_PAGE_COMPONENTS
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!define MUI_FINISHPAGE_RUN "$INSTDIR\LocalQA.bat"
!insertmacro MUI_PAGE_FINISH

# Uninstall pages
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES

# Language files
!insertmacro MUI_LANGUAGE "SimpChinese"

# Version Information
VIProductVersion "*******"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "ProductName" "${PRODUCT_NAME}"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "Comments" "AI-powered Company Policy Query Platform"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "CompanyName" "${PRODUCT_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "LegalTrademarks" "Test Application is a trademark of ${PRODUCT_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "FileDescription" "${PRODUCT_NAME}"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "FileVersion" "${PRODUCT_VERSION}"

# Installation components
Section "Main Program" SEC01
  SetOutPath "$INSTDIR"
  SetOverwrite ifnewer

  # Copy Python environment
  File /r "build_portable\python"

  # Copy application
  File /r "build_portable\app"

  # Copy large model file from original location
  DetailPrint "Copying large model file..."
  CopyFiles "$EXEDIR\model_large.safetensors" "$INSTDIR\app\models\qwen-1.5-1.8b-chat\model.safetensors"

  # Copy startup scripts
  File "build_portable\LocalQA.bat"
  File "build_portable\start_localqa.py"

  # Create desktop shortcut
  CreateShortCut "$DESKTOP\LocalQA.lnk" "$INSTDIR\LocalQA.bat"

  # Create start menu shortcuts
  CreateDirectory "$SMPROGRAMS\LocalQA"
  CreateShortCut "$SMPROGRAMS\LocalQA\LocalQA.lnk" "$INSTDIR\LocalQA.bat"
  CreateShortCut "$SMPROGRAMS\LocalQA\Uninstall LocalQA.lnk" "$INSTDIR\uninst.exe"
SectionEnd

Section "Visual C++ Runtime" SEC02
  # Check and install VC++ Redistributable
  SetOutPath "$TEMP"
  File "vcredist_x64.exe"
  ExecWait "$TEMP\vcredist_x64.exe /quiet /norestart"
  Delete "$TEMP\vcredist_x64.exe"
SectionEnd

Section -AdditionalIcons
  WriteIniStr "$INSTDIR\${PRODUCT_NAME}.url" "InternetShortcut" "URL" "${PRODUCT_WEB_SITE}"
  CreateShortCut "$SMPROGRAMS\LocalQA\网站.lnk" "$INSTDIR\${PRODUCT_NAME}.url"
SectionEnd

Section -Post
  WriteUninstaller "$INSTDIR\uninst.exe"
  WriteRegStr HKLM "${PRODUCT_DIR_REGKEY}" "" "$INSTDIR\LocalQA.bat"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayName" "$(^Name)"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "UninstallString" "$INSTDIR\uninst.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayIcon" "$INSTDIR\LocalQA.bat"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayVersion" "${PRODUCT_VERSION}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "URLInfoAbout" "${PRODUCT_WEB_SITE}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "Publisher" "${PRODUCT_PUBLISHER}"
SectionEnd

# Component descriptions
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC01} "LocalQA main program with complete Python environment and AI models"
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC02} "Microsoft Visual C++ Runtime required by some components"
!insertmacro MUI_FUNCTION_DESCRIPTION_END

# Uninstaller
Section Uninstall
  Delete "$INSTDIR\${PRODUCT_NAME}.url"
  Delete "$INSTDIR\uninst.exe"
  Delete "$INSTDIR\LocalQA.bat"
  Delete "$INSTDIR\start_localqa.py"
  
  RMDir /r "$INSTDIR\python"
  RMDir /r "$INSTDIR\app"
  RMDir "$INSTDIR"

  Delete "$SMPROGRAMS\LocalQA\卸载LocalQA.lnk"
  Delete "$SMPROGRAMS\LocalQA\网站.lnk"
  Delete "$SMPROGRAMS\LocalQA\LocalQA公司制度查询.lnk"
  RMDir "$SMPROGRAMS\LocalQA"
  Delete "$DESKTOP\LocalQA公司制度查询.lnk"

  DeleteRegKey ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}"
  DeleteRegKey HKLM "${PRODUCT_DIR_REGKEY}"
  SetAutoClose true
SectionEnd
