"""
性能监控模块
监控系统资源使用情况和性能指标
"""
import time
import threading
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, field
from collections import deque
import statistics

from .logger import get_logger
from .helpers import get_memory_usage

logger = get_logger(__name__)


@dataclass
class PerformanceMetric:
    """性能指标"""
    name: str
    value: float
    timestamp: float
    unit: str = ""
    tags: Dict[str, str] = field(default_factory=dict)


@dataclass
class PerformanceStats:
    """性能统计"""
    avg: float
    min: float
    max: float
    count: int
    latest: float


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.metrics: Dict[str, deque] = {}
        self.callbacks: Dict[str, List[Callable]] = {}
        self._lock = threading.RLock()
        self._running = False
        self._monitor_thread: Optional[threading.Thread] = None
        
        # 预定义的监控指标
        self.system_metrics = [
            'memory_usage_percent',
            'memory_usage_mb',
            'cpu_usage_percent'
        ]
    
    def start_monitoring(self, interval: float = 5.0):
        """开始监控"""
        if self._running:
            logger.warning("性能监控已在运行")
            return
        
        self._running = True
        self._monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self._monitor_thread.start()
        logger.info(f"性能监控已启动，监控间隔: {interval}秒")
    
    def stop_monitoring(self):
        """停止监控"""
        self._running = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=1.0)
        logger.info("性能监控已停止")
    
    def _monitor_loop(self, interval: float):
        """监控循环"""
        while self._running:
            try:
                self._collect_system_metrics()
                time.sleep(interval)
            except Exception as e:
                logger.error(f"性能监控循环出错: {e}")
                time.sleep(interval)
    
    def _collect_system_metrics(self):
        """收集系统指标"""
        try:
            # 内存使用情况
            memory_info = get_memory_usage()
            self.record_metric('memory_usage_percent', memory_info['percent'], unit='%')
            self.record_metric('memory_usage_mb', memory_info['rss_mb'], unit='MB')
            
            # CPU使用情况（如果可用）
            try:
                import psutil
                cpu_percent = psutil.cpu_percent(interval=None)
                self.record_metric('cpu_usage_percent', cpu_percent, unit='%')
            except ImportError:
                pass
                
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
    
    def record_metric(self, name: str, value: float, unit: str = "", tags: Dict[str, str] = None):
        """记录性能指标"""
        with self._lock:
            if name not in self.metrics:
                self.metrics[name] = deque(maxlen=self.max_history)
            
            metric = PerformanceMetric(
                name=name,
                value=value,
                timestamp=time.time(),
                unit=unit,
                tags=tags or {}
            )
            
            self.metrics[name].append(metric)
            
            # 触发回调
            if name in self.callbacks:
                for callback in self.callbacks[name]:
                    try:
                        callback(metric)
                    except Exception as e:
                        logger.error(f"性能监控回调执行失败: {e}")
    
    def get_stats(self, name: str, window_seconds: Optional[float] = None) -> Optional[PerformanceStats]:
        """获取指标统计"""
        with self._lock:
            if name not in self.metrics or not self.metrics[name]:
                return None
            
            metrics = list(self.metrics[name])
            
            # 时间窗口过滤
            if window_seconds:
                cutoff_time = time.time() - window_seconds
                metrics = [m for m in metrics if m.timestamp >= cutoff_time]
            
            if not metrics:
                return None
            
            values = [m.value for m in metrics]
            
            return PerformanceStats(
                avg=statistics.mean(values),
                min=min(values),
                max=max(values),
                count=len(values),
                latest=values[-1]
            )
    
    def get_all_stats(self, window_seconds: Optional[float] = None) -> Dict[str, PerformanceStats]:
        """获取所有指标统计"""
        stats = {}
        for name in self.metrics:
            stat = self.get_stats(name, window_seconds)
            if stat:
                stats[name] = stat
        return stats
    
    def add_callback(self, metric_name: str, callback: Callable[[PerformanceMetric], None]):
        """添加指标回调"""
        with self._lock:
            if metric_name not in self.callbacks:
                self.callbacks[metric_name] = []
            self.callbacks[metric_name].append(callback)
    
    def remove_callback(self, metric_name: str, callback: Callable):
        """移除指标回调"""
        with self._lock:
            if metric_name in self.callbacks:
                try:
                    self.callbacks[metric_name].remove(callback)
                except ValueError:
                    pass
    
    def clear_metrics(self, name: Optional[str] = None):
        """清空指标数据"""
        with self._lock:
            if name:
                if name in self.metrics:
                    self.metrics[name].clear()
            else:
                for metric_queue in self.metrics.values():
                    metric_queue.clear()
    
    def get_metric_names(self) -> List[str]:
        """获取所有指标名称"""
        with self._lock:
            return list(self.metrics.keys())
    
    def export_metrics(self, format: str = 'dict') -> Any:
        """导出指标数据"""
        with self._lock:
            if format == 'dict':
                return {
                    name: [
                        {
                            'value': m.value,
                            'timestamp': m.timestamp,
                            'unit': m.unit,
                            'tags': m.tags
                        }
                        for m in metrics
                    ]
                    for name, metrics in self.metrics.items()
                }
            else:
                raise ValueError(f"不支持的导出格式: {format}")


class PerformanceTimer:
    """性能计时器上下文管理器"""
    
    def __init__(self, name: str, monitor: Optional[PerformanceMonitor] = None):
        self.name = name
        self.monitor = monitor
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        duration = self.end_time - self.start_time
        
        if self.monitor:
            self.monitor.record_metric(f"{self.name}_duration", duration * 1000, unit='ms')
        
        logger.debug(f"性能计时 {self.name}: {duration:.3f}秒")
    
    @property
    def duration(self) -> Optional[float]:
        """获取持续时间（秒）"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None


def performance_timer(name: str, monitor: Optional[PerformanceMonitor] = None):
    """性能计时装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with PerformanceTimer(f"{func.__name__}_{name}", monitor):
                return func(*args, **kwargs)
        return wrapper
    return decorator


# 全局性能监控器实例
_global_monitor: Optional[PerformanceMonitor] = None


def get_performance_monitor() -> PerformanceMonitor:
    """获取全局性能监控器"""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = PerformanceMonitor()
    return _global_monitor


def start_global_monitoring(interval: float = 5.0):
    """启动全局性能监控"""
    monitor = get_performance_monitor()
    monitor.start_monitoring(interval)


def stop_global_monitoring():
    """停止全局性能监控"""
    global _global_monitor
    if _global_monitor:
        _global_monitor.stop_monitoring()
