#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型文件检测和安装脚本
"""

import os
import sys
import shutil
import py7zr
import argparse
from pathlib import Path

class ModelInstaller:
    def __init__(self):
        self.app_dir = Path(__file__).parent
        self.models_dir = self.app_dir / "models"
        self.required_models = [
            "qwen-1.5-1.8b-chat/model.safetensors",
            "qwen-1.5-1.8b-chat/config.json",
            "shibing624/text2vec-base-chinese"
        ]
        
    def check_models(self):
        """检查模型文件是否存在"""
        missing_models = []
        
        for model_path in self.required_models:
            full_path = self.models_dir / model_path
            if not full_path.exists():
                missing_models.append(model_path)
                
        return missing_models
        
    def install_models_from_archive(self, archive_path):
        """从压缩包安装模型文件"""
        archive_path = Path(archive_path)
        
        if not archive_path.exists():
            print(f"模型包不存在: {archive_path}")
            return False
            
        print(f"正在从 {archive_path} 安装模型文件...")
        
        try:
            if archive_path.suffix.lower() == '.7z':
                with py7zr.SevenZipFile(archive_path, mode='r') as archive:
                    archive.extractall(path=self.models_dir)
            else:
                print(f"不支持的压缩格式: {archive_path.suffix}")
                return False
                
            print("模型文件安装完成")
            return True
            
        except Exception as e:
            print(f"安装模型文件时出错: {e}")
            return False
            
    def auto_install(self):
        """自动检测和安装模型文件"""
        # 检查当前目录下的模型包
        possible_locations = [
            Path("../LocalQA_Models.7z"),
            Path("./LocalQA_Models.7z"),
            Path("../../LocalQA_Models.7z")
        ]
        
        for location in possible_locations:
            if location.exists():
                print(f"发现模型包: {location}")
                if self.install_models_from_archive(location):
                    return True
                    
        print("未找到模型包，请手动下载并安装")
        return False

def main():
    parser = argparse.ArgumentParser(description="模型文件管理工具")
    parser.add_argument("--check", action="store_true", help="检查模型文件")
    parser.add_argument("--install", type=str, help="从指定压缩包安装模型")
    parser.add_argument("--auto-install", action="store_true", help="自动安装模型")
    
    args = parser.parse_args()
    installer = ModelInstaller()
    
    if args.check:
        missing = installer.check_models()
        if missing:
            print("缺失的模型文件:")
            for model in missing:
                print(f"  - {model}")
            sys.exit(1)
        else:
            print("所有模型文件已就绪")
            sys.exit(0)
            
    elif args.install:
        success = installer.install_models_from_archive(args.install)
        sys.exit(0 if success else 1)
        
    elif args.auto_install:
        success = installer.auto_install()
        sys.exit(0 if success else 1)
        
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
