#!/usr/bin/env python3
"""
准备便携式Python环境用于NSIS打包
"""
import os
import sys
import shutil
import subprocess
import zipfile
from pathlib import Path
import requests
from urllib.parse import urlparse

class PortableEnvBuilder:
    def __init__(self, build_dir="build_portable"):
        self.build_dir = Path(build_dir)
        self.python_dir = self.build_dir / "python"
        self.app_dir = self.build_dir / "app"
        
    def download_portable_python(self):
        """下载便携式Python"""
        print("下载便携式Python...")
        
        # Python 3.11.x 便携版下载链接
        python_url = "https://www.python.org/ftp/python/3.11.8/python-3.11.8-embed-amd64.zip"
        python_zip = self.build_dir / "python-embed.zip"
        
        self.build_dir.mkdir(exist_ok=True)
        
        if not python_zip.exists():
            response = requests.get(python_url, stream=True)
            with open(python_zip, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            print("Python下载完成")
        
        # 解压Python
        if not self.python_dir.exists():
            with zipfile.ZipFile(python_zip, 'r') as zip_ref:
                zip_ref.extractall(self.python_dir)
            print("Python解压完成")
            
        # 配置Python环境
        self.configure_python()
        
    def configure_python(self):
        """配置Python环境"""
        print("配置Python环境...")
        
        # 修改python311._pth文件以启用site-packages
        pth_file = self.python_dir / "python311._pth"
        if pth_file.exists():
            content = pth_file.read_text()
            if "#import site" in content:
                content = content.replace("#import site", "import site")
                pth_file.write_text(content)
        
        # 下载并安装pip
        get_pip_url = "https://bootstrap.pypa.io/get-pip.py"
        get_pip_file = self.build_dir / "get-pip.py"
        
        if not get_pip_file.exists():
            response = requests.get(get_pip_url)
            get_pip_file.write_bytes(response.content)
        
        # 安装pip
        python_exe = self.python_dir / "python.exe"
        subprocess.run([str(python_exe), str(get_pip_file)], check=True)
        print("pip安装完成")
        
    def install_dependencies(self):
        """安装项目依赖"""
        print("安装项目依赖...")
        
        python_exe = self.python_dir / "python.exe"
        pip_exe = self.python_dir / "Scripts" / "pip.exe"
        
        # 安装依赖
        requirements_file = Path("requirements.txt")
        if requirements_file.exists():
            cmd = [str(pip_exe), "install", "-r", str(requirements_file)]
            subprocess.run(cmd, check=True)
            print("依赖安装完成")
        
    def copy_application(self):
        """复制应用程序文件"""
        print("复制应用程序文件...")
        
        self.app_dir.mkdir(exist_ok=True)
        
        # 复制主要文件和目录
        items_to_copy = [
            "main.py",
            "preprocess.py", 
            "config.yaml",
            "src/",
            "static/",
            "models/",
            "docs/"
        ]
        
        for item in items_to_copy:
            src = Path(item)
            if src.exists():
                if src.is_file():
                    shutil.copy2(src, self.app_dir)
                else:
                    dst = self.app_dir / src.name
                    if dst.exists():
                        shutil.rmtree(dst)
                    shutil.copytree(src, dst)
                print(f"已复制: {item}")
        
        # 创建数据目录
        (self.app_dir / "data").mkdir(exist_ok=True)
        (self.app_dir / "logs").mkdir(exist_ok=True)
        
    def create_launcher(self):
        """创建启动脚本"""
        print("创建启动脚本...")
        
        # 创建批处理启动脚本
        launcher_bat = self.build_dir / "LocalQA.bat"
        launcher_content = '''@echo off
cd /d "%~dp0"
set PYTHONPATH=%~dp0app
python\\python.exe app\\main.py
pause
'''
        launcher_bat.write_text(launcher_content, encoding='gbk')
        
        # 创建Python启动脚本
        launcher_py = self.build_dir / "start_localqa.py"
        launcher_py_content = '''#!/usr/bin/env python3
import os
import sys
from pathlib import Path

# 设置路径
current_dir = Path(__file__).parent
app_dir = current_dir / "app"
python_dir = current_dir / "python"

# 添加应用目录到Python路径
sys.path.insert(0, str(app_dir))

# 切换到应用目录
os.chdir(app_dir)

# 导入并运行主程序
try:
    import main
    main.main()
except Exception as e:
    print(f"启动失败: {e}")
    input("按回车键退出...")
'''
        launcher_py.write_text(launcher_py_content)
        
        print("启动脚本创建完成")
        
    def build(self):
        """构建便携式环境"""
        print("开始构建便携式Python环境...")
        
        self.download_portable_python()
        self.install_dependencies()
        self.copy_application()
        self.create_launcher()
        
        print(f"便携式环境构建完成: {self.build_dir}")
        print("可以使用NSIS打包此目录")

if __name__ == "__main__":
    builder = PortableEnvBuilder()
    builder.build()
