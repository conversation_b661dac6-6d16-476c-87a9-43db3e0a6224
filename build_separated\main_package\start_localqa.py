#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import sys
import subprocess
from pathlib import Path

def main():
    # 获取脚本所在目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # 检查模型文件
    python_exe = script_dir / "python" / "python.exe"
    model_check = subprocess.run([
        str(python_exe), "app/model_installer.py", "--check"
    ], capture_output=True)
    
    if model_check.returncode != 0:
        print("模型文件缺失，请先安装模型包")
        input("按回车键退出...")
        return 1
        
    # 启动应用
    subprocess.run([str(python_exe), "app/main.py"])
    return 0

if __name__ == "__main__":
    sys.exit(main())
