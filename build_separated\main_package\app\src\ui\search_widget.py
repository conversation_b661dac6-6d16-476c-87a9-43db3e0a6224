"""
制度检索界面组件
"""
from typing import List, Optional

try:
    from PyQt6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLineEdit, 
        QPushButton, QLabel, QListWidget, QListWidgetItem,
        QComboBox, QFrame, QSplitter, QTextEdit, QCheckBox
    )
    from PyQt6.QtCore import Qt, QThread, pyqtSignal
    from PyQt6.QtGui import QFont
except ImportError as e:
    raise e

from ..utils.logger import get_logger
from ..core.search_system import get_search_system

logger = get_logger(__name__)


class SearchThread(QThread):
    """搜索线程"""
    results_ready = pyqtSignal(list)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, query: str, search_type: str, department: str = "", doc_type: str = ""):
        super().__init__()
        self.query = query
        self.search_type = search_type
        self.department = department
        self.doc_type = doc_type
    
    def run(self):
        """执行搜索"""
        try:
            search_system = get_search_system()
            
            if self.search_type == "vector":
                results = search_system.vector_search(self.query, top_k=20)
            elif self.search_type == "text":
                dept = self.department if self.department != "全部" else None
                dtype = self.doc_type if self.doc_type != "全部" else None
                results = search_system.text_search(self.query, top_k=20, 
                                                  department=dept, document_type=dtype)
            else:  # hybrid
                results = search_system.hybrid_search(self.query, top_k=20)
            
            # 转换为字典格式
            results_dict = [result.to_dict() if hasattr(result, 'to_dict') else result 
                           for result in results]
            
            self.results_ready.emit(results_dict)
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            self.error_occurred.emit(str(e))


class SearchResultItem(QFrame):
    """搜索结果项"""
    
    def __init__(self, result_data: dict, parent=None):
        super().__init__(parent)
        self.result_data = result_data
        self._init_ui()
    
    def _init_ui(self):
        """初始化界面"""
        try:
            self.setFrameStyle(QFrame.Shape.Box)
            self.setStyleSheet("""
                QFrame {
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    background-color: white;
                    margin: 2px;
                    padding: 8px;
                }
                QFrame:hover {
                    background-color: #f0f8ff;
                    border-color: #007acc;
                }
            """)
            
            layout = QVBoxLayout(self)
            layout.setContentsMargins(10, 10, 10, 10)
            
            # 标题行
            title_layout = QHBoxLayout()
            
            # 文件名
            file_name = self.result_data.get('file_name', '未知文件')
            title_label = QLabel(f"📄 {file_name}")
            title_label.setFont(QFont("", 10, QFont.Weight.Bold))
            title_layout.addWidget(title_label)
            
            title_layout.addStretch()
            
            # 分数
            score = self.result_data.get('score', 0)
            score_label = QLabel(f"分数: {score:.3f}")
            score_label.setFont(QFont("", 8))
            score_label.setStyleSheet("color: #007acc;")
            title_layout.addWidget(score_label)
            
            layout.addLayout(title_layout)
            
            # 信息行
            info_layout = QHBoxLayout()
            
            # 部门
            department = self.result_data.get('department', '未知部门')
            dept_label = QLabel(f"🏢 {department}")
            dept_label.setFont(QFont("", 8))
            dept_label.setStyleSheet("color: #666;")
            info_layout.addWidget(dept_label)
            
            # 页码
            page_number = self.result_data.get('page_number')
            if page_number:
                page_label = QLabel(f"📖 第{page_number}页")
                page_label.setFont(QFont("", 8))
                page_label.setStyleSheet("color: #666;")
                info_layout.addWidget(page_label)
            
            # 搜索类型
            search_type = self.result_data.get('search_type', '')
            if search_type:
                type_label = QLabel(f"🔍 {search_type}")
                type_label.setFont(QFont("", 8))
                type_label.setStyleSheet("color: #666;")
                info_layout.addWidget(type_label)
            
            info_layout.addStretch()
            layout.addLayout(info_layout)
            
            # 内容预览
            content = self.result_data.get('content', '')
            preview = content[:200] + "..." if len(content) > 200 else content
            content_label = QLabel(preview)
            content_label.setFont(QFont("", 9))
            content_label.setWordWrap(True)
            content_label.setStyleSheet("color: #333; margin-top: 8px; line-height: 1.4;")
            layout.addWidget(content_label)
            
        except Exception as e:
            logger.error(f"搜索结果项界面初始化失败: {e}")
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        try:
            if event.button() == Qt.MouseButton.LeftButton:
                # 发送预览请求信号
                file_path = self.result_data.get('source_file', '')
                page_number = self.result_data.get('page_number', 1)
                
                if file_path:
                    # 通过父组件发送信号
                    parent_widget = self.parent()
                    while parent_widget and not hasattr(parent_widget, 'preview_requested'):
                        parent_widget = parent_widget.parent()
                    
                    if parent_widget and hasattr(parent_widget, 'preview_requested'):
                        parent_widget.preview_requested.emit(file_path, page_number)
            
        except Exception as e:
            logger.error(f"搜索结果项点击处理失败: {e}")


class SearchWidget(QWidget):
    """制度检索组件"""
    preview_requested = pyqtSignal(str, int)  # 文件路径, 页码
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.search_system = None
        self.search_thread = None
        self.current_results = []
        
        self._init_ui()
        self._init_search_system()
        self._load_filter_options()
    
    def _init_ui(self):
        """初始化界面"""
        try:
            layout = QVBoxLayout(self)
            
            # 搜索区域
            self._create_search_area(layout)
            
            # 结果区域
            self._create_results_area(layout)
            
        except Exception as e:
            logger.error(f"搜索界面初始化失败: {e}")
    
    def _create_search_area(self, parent_layout):
        """创建搜索区域"""
        try:
            search_frame = QFrame()
            search_frame.setStyleSheet("""
                QFrame {
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    background-color: #f9f9f9;
                    padding: 10px;
                }
            """)
            search_layout = QVBoxLayout(search_frame)
            
            # 标题
            title_label = QLabel("🔍 制度文档检索")
            title_label.setFont(QFont("", 12, QFont.Weight.Bold))
            title_label.setStyleSheet("color: #333; margin-bottom: 10px;")
            search_layout.addWidget(title_label)
            
            # 搜索输入
            input_layout = QHBoxLayout()
            
            self.search_input = QLineEdit()
            self.search_input.setPlaceholderText("请输入关键词，如：安全管理、财务制度、培训规定...")
            self.search_input.setFont(QFont("", 10))
            self.search_input.setStyleSheet("""
                QLineEdit {
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    padding: 8px;
                    background-color: white;
                }
            """)
            self.search_input.returnPressed.connect(self._on_search_clicked)
            input_layout.addWidget(self.search_input)
            
            # 搜索按钮
            self.search_button = QPushButton("搜索")
            self.search_button.setFont(QFont("", 10))
            self.search_button.setStyleSheet("""
                QPushButton {
                    background-color: #007acc;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 8px 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #005a9e;
                }
                QPushButton:disabled {
                    background-color: #ccc;
                }
            """)
            self.search_button.clicked.connect(self._on_search_clicked)
            input_layout.addWidget(self.search_button)
            
            search_layout.addLayout(input_layout)
            
            # 过滤选项
            filter_layout = QHBoxLayout()
            
            # 搜索类型
            type_label = QLabel("搜索类型:")
            type_label.setFont(QFont("", 9))
            filter_layout.addWidget(type_label)
            
            self.search_type_combo = QComboBox()
            self.search_type_combo.addItems(["混合搜索", "语义搜索", "关键词搜索"])
            self.search_type_combo.setFont(QFont("", 9))
            filter_layout.addWidget(self.search_type_combo)
            
            # 部门过滤
            dept_label = QLabel("部门:")
            dept_label.setFont(QFont("", 9))
            filter_layout.addWidget(dept_label)
            
            self.department_combo = QComboBox()
            self.department_combo.setFont(QFont("", 9))
            filter_layout.addWidget(self.department_combo)
            
            # 文档类型过滤
            type_label = QLabel("类型:")
            type_label.setFont(QFont("", 9))
            filter_layout.addWidget(type_label)
            
            self.doc_type_combo = QComboBox()
            self.doc_type_combo.setFont(QFont("", 9))
            filter_layout.addWidget(self.doc_type_combo)
            
            filter_layout.addStretch()
            search_layout.addLayout(filter_layout)
            
            parent_layout.addWidget(search_frame)
            
        except Exception as e:
            logger.error(f"搜索区域创建失败: {e}")
    
    def _create_results_area(self, parent_layout):
        """创建结果区域"""
        try:
            results_frame = QFrame()
            results_layout = QVBoxLayout(results_frame)
            
            # 结果标题
            self.results_title = QLabel("搜索结果")
            self.results_title.setFont(QFont("", 11, QFont.Weight.Bold))
            self.results_title.setStyleSheet("color: #333; margin-bottom: 5px;")
            results_layout.addWidget(self.results_title)
            
            # 结果列表
            self.results_list = QListWidget()
            self.results_list.setStyleSheet("""
                QListWidget {
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    background-color: white;
                }
                QListWidget::item {
                    border: none;
                    padding: 0px;
                }
            """)
            results_layout.addWidget(self.results_list)
            
            parent_layout.addWidget(results_frame)
            
        except Exception as e:
            logger.error(f"结果区域创建失败: {e}")
    
    def _init_search_system(self):
        """初始化搜索系统"""
        try:
            self.search_system = get_search_system()
        except Exception as e:
            logger.error(f"搜索系统初始化失败: {e}")
    
    def _load_filter_options(self):
        """加载过滤选项"""
        try:
            if not self.search_system:
                return
            
            # 加载部门列表
            departments = ["全部"] + self.search_system.get_departments()
            self.department_combo.clear()
            self.department_combo.addItems(departments)
            
            # 加载文档类型列表
            doc_types = ["全部"] + self.search_system.get_document_types()
            self.doc_type_combo.clear()
            self.doc_type_combo.addItems(doc_types)
            
        except Exception as e:
            logger.error(f"过滤选项加载失败: {e}")
    
    def _on_search_clicked(self):
        """搜索按钮点击事件"""
        try:
            query = self.search_input.text().strip()
            if not query:
                return
            
            # 禁用搜索按钮
            self.search_button.setEnabled(False)
            self.search_button.setText("搜索中...")
            
            # 清空现有结果
            self.results_list.clear()
            self.current_results = []
            
            # 获取搜索参数
            search_type_text = self.search_type_combo.currentText()
            search_type = {
                "混合搜索": "hybrid",
                "语义搜索": "vector", 
                "关键词搜索": "text"
            }.get(search_type_text, "hybrid")
            
            department = self.department_combo.currentText()
            doc_type = self.doc_type_combo.currentText()
            
            # 启动搜索线程
            self.search_thread = SearchThread(query, search_type, department, doc_type)
            self.search_thread.results_ready.connect(self._on_results_ready)
            self.search_thread.error_occurred.connect(self._on_error_occurred)
            self.search_thread.start()
            
        except Exception as e:
            logger.error(f"搜索处理失败: {e}")
            self._reset_search_button()
    
    def _on_results_ready(self, results: List[dict]):
        """处理搜索结果"""
        try:
            self.current_results = results
            
            # 更新结果标题
            self.results_title.setText(f"搜索结果 ({len(results)} 条)")
            
            # 显示结果
            for result in results:
                self._add_result_item(result)
            
            # 如果没有结果
            if not results:
                self._add_no_results_item()
            
            # 重置搜索按钮
            self._reset_search_button()
            
        except Exception as e:
            logger.error(f"结果处理失败: {e}")
            self._reset_search_button()
    
    def _on_error_occurred(self, error: str):
        """处理搜索错误"""
        try:
            self.results_title.setText(f"搜索出错: {error}")
            self._add_error_item(error)
            self._reset_search_button()
            
        except Exception as e:
            logger.error(f"错误处理失败: {e}")
            self._reset_search_button()
    
    def _add_result_item(self, result: dict):
        """添加结果项"""
        try:
            # 创建结果项组件
            result_item = SearchResultItem(result)
            
            # 创建列表项
            list_item = QListWidgetItem()
            list_item.setSizeHint(result_item.sizeHint())
            
            # 添加到列表
            self.results_list.addItem(list_item)
            self.results_list.setItemWidget(list_item, result_item)
            
        except Exception as e:
            logger.error(f"添加结果项失败: {e}")
    
    def _add_no_results_item(self):
        """添加无结果项"""
        try:
            no_results_label = QLabel("未找到相关结果，请尝试其他关键词")
            no_results_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            no_results_label.setStyleSheet("color: #666; font-style: italic; padding: 20px;")
            
            list_item = QListWidgetItem()
            list_item.setSizeHint(no_results_label.sizeHint())
            
            self.results_list.addItem(list_item)
            self.results_list.setItemWidget(list_item, no_results_label)
            
        except Exception as e:
            logger.error(f"添加无结果项失败: {e}")
    
    def _add_error_item(self, error: str):
        """添加错误项"""
        try:
            error_label = QLabel(f"搜索出错: {error}")
            error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            error_label.setStyleSheet("color: #d32f2f; padding: 20px;")
            
            list_item = QListWidgetItem()
            list_item.setSizeHint(error_label.sizeHint())
            
            self.results_list.addItem(list_item)
            self.results_list.setItemWidget(list_item, error_label)
            
        except Exception as e:
            logger.error(f"添加错误项失败: {e}")
    
    def _reset_search_button(self):
        """重置搜索按钮"""
        try:
            self.search_button.setEnabled(True)
            self.search_button.setText("搜索")
            
        except Exception as e:
            logger.error(f"重置搜索按钮失败: {e}")
